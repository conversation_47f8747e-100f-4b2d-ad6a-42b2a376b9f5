import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

interface ScrapeOptions {
  waitForSelector?: string;
  timeout?: number;
  captureScreenshot?: boolean;
  extractImages?: boolean;
  extractFavicon?: boolean;
  scrapePricingPage?: boolean;
  scrapeFAQPage?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const { url, options = {} }: { url: string; options: ScrapeOptions } = await request.json();

    if (!url || !url.startsWith('http')) {
      return NextResponse.json(
        { success: false, error: 'Invalid URL provided' },
        { status: 400 }
      );
    }

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
    });

    const page = await browser.newPage();

    try {
      // Set viewport and user agent
      await page.setViewport({ width: 1200, height: 800 });
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: options.timeout || 30000
      });

      if (options.waitForSelector) {
        await page.waitForSelector(options.waitForSelector, { timeout: 10000 });
      }

      // Extract main page content
      const scrapedData = await page.evaluate(() => {
        // Extract text content
        const textContent = document.body.innerText || '';

        // Extract meta information
        const title = document.title || '';
        const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
        const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content') || '';
        const ogDescription = document.querySelector('meta[property="og:description"]')?.getAttribute('content') || '';

        // Extract images
        const images = Array.from(document.querySelectorAll('img')).map(img => ({
          src: img.src,
          alt: img.alt || '',
          width: img.naturalWidth,
          height: img.naturalHeight
        })).filter(img => img.src && img.width > 100 && img.height > 100);

        // Extract favicon
        const favicon = document.querySelector('link[rel="icon"]')?.getAttribute('href') ||
                       document.querySelector('link[rel="shortcut icon"]')?.getAttribute('href') ||
                       '/favicon.ico';

        // Extract pricing information from main page
        const pricingElements = document.querySelectorAll('[class*="price"], [class*="cost"], [class*="plan"], [id*="price"], [id*="pricing"]');
        const pricingText = Array.from(pricingElements).map(el => el.textContent?.trim()).filter(Boolean).join(' ');

        // Extract FAQ information from main page
        const faqElements = document.querySelectorAll('[class*="faq"], [class*="question"], [class*="accordion"], [id*="faq"]');
        const faqText = Array.from(faqElements).map(el => el.textContent?.trim()).filter(Boolean).join(' ');

        // Look for pricing and FAQ page links
        const allLinks = Array.from(document.querySelectorAll('a[href]'));
        const pricingLinks = allLinks.filter(link => {
          const href = link.getAttribute('href')?.toLowerCase() || '';
          const text = link.textContent?.toLowerCase() || '';
          return href.includes('/pricing') || href.includes('/plans') || href.includes('/cost') ||
                 text.includes('pricing') || text.includes('plans') || text.includes('cost');
        });

        const faqLinks = allLinks.filter(link => {
          const href = link.getAttribute('href')?.toLowerCase() || '';
          const text = link.textContent?.toLowerCase() || '';
          return href.includes('/faq') || href.includes('/help') || href.includes('/support') ||
                 text.includes('faq') || text.includes('help') || text.includes('support');
        });

        return {
          title: ogTitle || title,
          description: ogDescription || metaDescription,
          textContent: textContent.substring(0, 5000), // Limit content length
          images: images.slice(0, 10), // Limit to 10 images
          favicon,
          pricingText,
          faqText,
          pricingLinks: pricingLinks.map(link => ({
            href: link.getAttribute('href'),
            text: link.textContent?.trim()
          })).slice(0, 3),
          faqLinks: faqLinks.map(link => ({
            href: link.getAttribute('href'),
            text: link.textContent?.trim()
          })).slice(0, 3),
          metadata: {
            ogTitle,
            ogDescription,
            metaDescription,
            url: window.location.href
          }
        };
      });

      // Capture screenshot if requested
      let screenshot = null;
      if (options.captureScreenshot) {
        screenshot = await page.screenshot({
          type: 'png',
          quality: parseInt(process.env.SCREENSHOT_QUALITY || '80'),
          fullPage: false,
          encoding: 'base64'
        });
      }

      await browser.close();

      return NextResponse.json({
        success: true,
        data: {
          ...scrapedData,
          screenshot: screenshot ? `data:image/png;base64,${screenshot}` : null
        }
      });

    } catch (error) {
      await browser.close();
      throw error;
    }

  } catch (error) {
    console.error('Scraping error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to scrape URL' },
      { status: 500 }
    );
  }
}
