{"name": "ai-dude-directory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "tsx scripts/migrate-data.ts", "db:setup": "npm run migrate", "test:jobs": "tsx scripts/test-jobs.ts", "test:automation": "tsx scripts/test-core-automation.ts", "test:e2e": "tsx scripts/test-end-to-end.ts", "health:check": "tsx scripts/production-health-check.ts", "inspect:data": "tsx scripts/inspect-scraped-data.ts", "demo:scraping": "tsx scripts/demo-scraping.ts", "production:verify": "npm run health:check && npm run test:e2e", "deploy:prepare": "npm run build && npm run production:verify", "monitor:start": "tsx scripts/production-monitor.ts", "cleanup:jobs": "tsx scripts/cleanup-completed-jobs.ts"}, "dependencies": {"@supabase/supabase-js": "^2.49.10", "@types/animejs": "^3.1.13", "@types/jsonwebtoken": "^9.0.9", "animejs": "^4.0.2", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "nodemailer": "^6.9.8", "openai": "^5.1.0", "puppeteer": "^24.10.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}