export interface AITool {
  id: string;
  name: string;
  logoUrl: string;
  description: string;
  link: string;
  tags?: {
    type: 'Trending' | 'New' | 'Premium';
    label?: string;
    icon?: string;
  }[];
  category: string;
  // Extended fields for tool detail pages
  detailedDescription?: string;
  features?: string[];
  screenshots?: string[];
  pricing?: {
    type: 'free' | 'freemium' | 'paid' | 'open source';
    plans?: {
      name: string;
      price: string;
      features: string[];
    }[];
  };
  reviews?: {
    rating: number;
    totalReviews: number;
    highlights?: string[];
  };
  website?: string;
  company?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  // New fields for redesigned tool detail page
  subcategory?: string;
  isVerified?: boolean;
  isClaimed?: boolean;
  claimInfo?: {
    isClaimable: boolean;
    claimUrl?: string;
    claimInstructions?: string;
  };
  prosAndCons?: {
    pros: string[];
    cons: string[];
  };
  releases?: {
    version: string;
    date: string;
    notes: string;
    isLatest?: boolean;
  }[];
}

export interface AICategory {
  id: string;
  title: string;
  iconName: string;
  description: string;
  tools: AITool[];
  totalToolsCount: number;
  seeAllButton: {
    colorClass: string;
    textColorClass: string;
  };
}

export interface TooltipData {
  content: string;
  targetRect: DOMRect | null;
  position: 'top' | 'bottom' | 'left' | 'right';
}

export interface TopSearch {
  term: string;
  link: string;
}

export interface ToolFilters {
  search?: string;
  tags?: string[];
  pricing?: 'free' | 'freemium' | 'paid' | 'open source';
  verified?: boolean;
  sortBy?: 'name' | 'rating' | 'newest' | 'popular';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface CategoryPageData {
  category: AICategory;
  subcategory?: string;
  tools: AITool[];
  filters: ToolFilters;
  pagination: PaginationInfo;
}

export interface HomePageState {
  searchTerm: string;
  isSearchDropdownVisible: boolean;
  topSearches: TopSearch[];
  allCategories: AICategory[];
  searchResults: AITool[] | null;
  isLoadingCategories: boolean;
  isLoadingSearchResults: boolean;
  activeTooltip: TooltipData | null;
  isScrollToTopVisible: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  isExpanded?: boolean;
}
