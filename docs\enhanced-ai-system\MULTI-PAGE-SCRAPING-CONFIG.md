# Multi-Page Scraping Configuration Guide

## Overview

This document provides comprehensive configuration options for the multi-page scraping system that can detect, scrape, and queue additional pages (pricing, FAQ, features, about) based on configurable settings and credit availability.

## Configuration Structure

### 1. Main Configuration Interface

```typescript
interface MultiPageScrapingConfig {
  enabled: boolean;                    // Master enable/disable switch
  mode: 'immediate' | 'queue_for_later' | 'conditional'; // Scraping strategy
  maxPagesPerTool: number;            // Maximum additional pages to scrape
  creditThreshold: number;            // Minimum credits before multi-page scraping
  
  pageTypes: {
    pricing: PageTypeConfig;
    faq: PageTypeConfig;
    features: PageTypeConfig;
    about: PageTypeConfig;
  };
  
  fallbackStrategy: FallbackStrategyConfig;
  queueManagement: QueueManagementConfig;
}

interface PageTypeConfig {
  enabled: boolean;                   // Enable this page type
  priority: 'high' | 'medium' | 'low'; // Scraping priority
  patterns: string[];                 // URL patterns to detect pages
  selectors: string[];               // CSS selectors for content detection
  required: boolean;                 // Whether to fail if not found
  maxRetries: number;                // Maximum retry attempts
  timeout: number;                   // Timeout for this page type
}

interface FallbackStrategyConfig {
  searchInMainPage: boolean;         // Look for content in main page first
  useNavigation: boolean;            // Follow navigation links
  useSitemap: boolean;              // Check sitemap.xml
  constructUrls: boolean;           // Try common URL patterns
}

interface QueueManagementConfig {
  enabled: boolean;                  // Enable queuing for later processing
  maxQueueSize: number;             // Maximum items in queue per tool
  processingDelay: number;          // Delay between queue processing (ms)
  batchSize: number;                // Number of items to process in batch
  retryFailedItems: boolean;        // Retry failed queue items
}
```

### 2. Default Configuration

```typescript
const DEFAULT_MULTI_PAGE_CONFIG: MultiPageScrapingConfig = {
  enabled: true,
  mode: 'conditional', // Smart decision based on content and credits
  maxPagesPerTool: 4,
  creditThreshold: 100, // Require 100+ credits for multi-page scraping
  
  pageTypes: {
    pricing: {
      enabled: true,
      priority: 'high',
      patterns: [
        '/pricing', '/price', '/plans', '/subscription',
        '/cost', '/buy', '/purchase', '/upgrade', '/billing'
      ],
      selectors: [
        '.pricing', '.plans', '.subscription', '.billing',
        '[class*="price"]', '[id*="pricing"]', '[class*="plan"]'
      ],
      required: true, // Pricing is critical for AI tool directory
      maxRetries: 2,
      timeout: 30000
    },
    
    faq: {
      enabled: true,
      priority: 'medium',
      patterns: [
        '/faq', '/help', '/support', '/questions',
        '/q-and-a', '/frequently-asked', '/helpdesk'
      ],
      selectors: [
        '.faq', '.help', '.support', '.questions',
        '[class*="faq"]', '[id*="faq"]', '[class*="help"]'
      ],
      required: false,
      maxRetries: 1,
      timeout: 25000
    },
    
    features: {
      enabled: true,
      priority: 'high',
      patterns: [
        '/features', '/capabilities', '/functionality',
        '/what-we-do', '/services', '/tools'
      ],
      selectors: [
        '.features', '.capabilities', '.functionality',
        '[class*="feature"]', '[id*="features"]', '[class*="capability"]'
      ],
      required: true, // Features are critical for understanding the tool
      maxRetries: 2,
      timeout: 30000
    },
    
    about: {
      enabled: true,
      priority: 'low',
      patterns: [
        '/about', '/about-us', '/company', '/story',
        '/mission', '/team', '/who-we-are'
      ],
      selectors: [
        '.about', '.company', '.story', '.mission',
        '[class*="about"]', '[id*="about"]', '[class*="company"]'
      ],
      required: false,
      maxRetries: 1,
      timeout: 20000
    }
  },
  
  fallbackStrategy: {
    searchInMainPage: true,    // Always check main page first (no extra cost)
    useNavigation: true,       // Follow navigation links
    useSitemap: false,         // Sitemap checking disabled by default
    constructUrls: true        // Try common URL patterns as fallback
  },
  
  queueManagement: {
    enabled: true,
    maxQueueSize: 10,          // Maximum 10 queued items per tool
    processingDelay: 5000,     // 5 second delay between processing
    batchSize: 3,              // Process 3 items at a time
    retryFailedItems: true
  }
};
```

## Configuration Modes

### 1. Immediate Mode
**Behavior**: Scrape all detected pages immediately if credits allow
**Use Case**: High-priority tools, sufficient credit budget
**Configuration**:
```typescript
{
  mode: 'immediate',
  creditThreshold: 200, // Higher threshold for immediate mode
  maxPagesPerTool: 4
}
```

### 2. Queue for Later Mode
**Behavior**: Always queue additional pages for later processing
**Use Case**: Credit conservation, batch processing workflows
**Configuration**:
```typescript
{
  mode: 'queue_for_later',
  creditThreshold: 50, // Lower threshold since we're queuing
  queueManagement: {
    enabled: true,
    processingDelay: 10000, // Longer delay for batch processing
    batchSize: 5
  }
}
```

### 3. Conditional Mode (Recommended)
**Behavior**: Smart decision based on content quality, credits, and priority
**Use Case**: Optimal balance of cost and completeness
**Configuration**:
```typescript
{
  mode: 'conditional',
  creditThreshold: 100,
  // High priority + high confidence = immediate scraping
  // Medium/low priority or low confidence = queue for later
  // Insufficient credits = queue for later
}
```

## Admin Panel Integration

### 1. Configuration UI Components

```typescript
interface AdminMultiPageSettings {
  // Global Settings
  globalEnabled: boolean;
  defaultMode: 'immediate' | 'queue_for_later' | 'conditional';
  globalCreditThreshold: number;
  
  // Per-Page Type Settings
  pageTypeSettings: {
    [K in 'pricing' | 'faq' | 'features' | 'about']: {
      enabled: boolean;
      priority: 'high' | 'medium' | 'low';
      required: boolean;
      customPatterns: string[]; // Admin can add custom URL patterns
      customSelectors: string[]; // Admin can add custom CSS selectors
    };
  };
  
  // Queue Management
  queueSettings: {
    enabled: boolean;
    maxQueueSize: number;
    processingSchedule: 'immediate' | 'hourly' | 'daily';
    batchSize: number;
  };
  
  // Monitoring
  monitoring: {
    trackSuccessRates: boolean;
    alertOnLowCredits: boolean;
    creditAlertThreshold: number;
  };
}
```

### 2. Runtime Configuration Updates

```typescript
class MultiPageConfigManager {
  async updateConfiguration(newConfig: Partial<MultiPageScrapingConfig>): Promise<void> {
    // Validate configuration
    const validatedConfig = this.validateConfiguration(newConfig);
    
    // Update database
    await this.saveConfiguration(validatedConfig);
    
    // Notify running processes
    await this.notifyConfigurationChange(validatedConfig);
    
    // Update queue processing if needed
    if (validatedConfig.queueManagement) {
      await this.updateQueueProcessing(validatedConfig.queueManagement);
    }
  }
  
  async getConfiguration(): Promise<MultiPageScrapingConfig> {
    const config = await this.loadConfiguration();
    return { ...DEFAULT_MULTI_PAGE_CONFIG, ...config };
  }
  
  private validateConfiguration(config: Partial<MultiPageScrapingConfig>): MultiPageScrapingConfig {
    // Validation logic
    if (config.creditThreshold && config.creditThreshold < 10) {
      throw new Error('Credit threshold must be at least 10');
    }
    
    if (config.maxPagesPerTool && config.maxPagesPerTool > 10) {
      throw new Error('Maximum pages per tool cannot exceed 10');
    }
    
    return { ...DEFAULT_MULTI_PAGE_CONFIG, ...config };
  }
}
```

## Usage Examples

### 1. High-Priority Tool (Immediate Scraping)
```typescript
const highPriorityConfig = {
  mode: 'immediate',
  creditThreshold: 150,
  pageTypes: {
    pricing: { enabled: true, priority: 'high', required: true },
    features: { enabled: true, priority: 'high', required: true },
    faq: { enabled: true, priority: 'medium', required: false },
    about: { enabled: false } // Skip about page for high-priority tools
  }
};
```

### 2. Credit-Conscious Configuration
```typescript
const creditConservativeConfig = {
  mode: 'queue_for_later',
  creditThreshold: 50,
  maxPagesPerTool: 2, // Limit to most important pages
  pageTypes: {
    pricing: { enabled: true, priority: 'high', required: true },
    features: { enabled: true, priority: 'high', required: true },
    faq: { enabled: false },
    about: { enabled: false }
  },
  queueManagement: {
    enabled: true,
    processingDelay: 30000, // 30 second delay between processing
    batchSize: 2
  }
};
```

### 3. Comprehensive Analysis Configuration
```typescript
const comprehensiveConfig = {
  mode: 'conditional',
  creditThreshold: 200,
  maxPagesPerTool: 4,
  pageTypes: {
    pricing: { enabled: true, priority: 'high', required: true },
    features: { enabled: true, priority: 'high', required: true },
    faq: { enabled: true, priority: 'medium', required: false },
    about: { enabled: true, priority: 'low', required: false }
  },
  fallbackStrategy: {
    searchInMainPage: true,
    useNavigation: true,
    useSitemap: true, // Enable sitemap checking for comprehensive analysis
    constructUrls: true
  }
};
```

## Monitoring and Analytics

### 1. Success Rate Tracking
```typescript
interface MultiPageAnalytics {
  totalToolsProcessed: number;
  pagesFoundByType: {
    pricing: { found: number; total: number; successRate: number };
    faq: { found: number; total: number; successRate: number };
    features: { found: number; total: number; successRate: number };
    about: { found: number; total: number; successRate: number };
  };
  discoveryMethods: {
    navigation: number;
    pattern: number;
    content: number;
    sitemap: number;
  };
  creditUsage: {
    totalCreditsUsed: number;
    averageCreditsPerTool: number;
    creditsSavedByQueuing: number;
  };
  queueStatistics: {
    itemsQueued: number;
    itemsProcessed: number;
    averageProcessingTime: number;
    failureRate: number;
  };
}
```

---

*This multi-page scraping configuration system provides flexible, cost-aware content collection that can be tailored to different use cases while maintaining optimal resource utilization and content quality.*
