# Configuration Management Guide

## Overview

This document defines the comprehensive configuration management system for the enhanced AI-powered content generation system, covering environment-based configuration, admin panel settings, security management, and deployment configurations.

## Configuration Architecture

### 1. Configuration Hierarchy
```mermaid
graph TB
    subgraph "Configuration Sources"
        ENV[Environment Variables]
        ADMIN[Admin Panel Settings]
        DB[Database Configuration]
        DEFAULTS[Default Values]
    end
    
    subgraph "Configuration Layers"
        RUNTIME[Runtime Configuration]
        CACHED[Cached Configuration]
        VALIDATED[Validated Configuration]
    end
    
    subgraph "Configuration Consumers"
        API[API Services]
        JOBS[Job Processing]
        AI[AI Generation]
        SCRAPING[Web Scraping]
        ADMIN_UI[Admin Interface]
    end
    
    ENV --> RUNTIME
    ADMIN --> RUNTIME
    DB --> RUNTIME
    DEFAULTS --> RUNTIME
    
    RUNTIME --> CACHED
    CACHED --> VALIDATED
    
    VALIDATED --> API
    VALIDATED --> JOBS
    VALIDATED --> AI
    VALIDATED --> SCRAPING
    VALIDATED --> ADMIN_UI
```

### 2. Configuration Management System
```typescript
interface ConfigurationManager {
  sources: {
    environment: EnvironmentConfigSource;
    adminPanel: AdminPanelConfigSource;
    database: DatabaseConfigSource;
    defaults: DefaultConfigSource;
  };
  
  layers: {
    runtime: RuntimeConfigLayer;
    cached: CachedConfigLayer;
    validated: ValidatedConfigLayer;
  };
  
  validation: {
    schema: ConfigurationSchema;
    rules: ValidationRule[];
    sanitization: SanitizationRule[];
  };
  
  security: {
    encryption: EncryptionConfig;
    accessControl: AccessControlConfig;
    auditLogging: AuditLoggingConfig;
  };
}
```

## Environment Configuration

### 1. Environment Variables Structure
```typescript
interface EnvironmentConfiguration {
  // Core System
  NODE_ENV: 'development' | 'staging' | 'production';
  PORT: number;
  SITE_URL: string;
  
  // Database
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  DATABASE_URL: string;
  
  // External APIs
  SCRAPE_DO_API_KEY: string;
  SCRAPE_DO_BASE_URL: string;
  OPENAI_API_KEY: string;
  OPENAI_ORGANIZATION: string;
  OPENROUTER_API_KEY: string;
  OPENROUTER_BASE_URL: string;
  
  // Job Processing
  JOB_QUEUE_REDIS_URL?: string;
  JOB_CONCURRENCY: number;
  JOB_TIMEOUT: number;
  JOB_RETRY_ATTEMPTS: number;
  
  // Security
  ADMIN_API_KEY: string;
  JWT_SECRET: string;
  ENCRYPTION_KEY: string;
  
  // Monitoring & Logging
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
  SENTRY_DSN?: string;
  ANALYTICS_API_KEY?: string;
  
  // Feature Flags
  ENABLE_AI_GENERATION: boolean;
  ENABLE_BULK_PROCESSING: boolean;
  ENABLE_ADMIN_PANEL: boolean;
  ENABLE_RATE_LIMITING: boolean;
  
  // Performance & Limits
  MAX_CONCURRENT_JOBS: number;
  MAX_CONTENT_SIZE: number;
  API_RATE_LIMIT: number;
  CACHE_TTL: number;
}

// Environment validation schema
const ENV_SCHEMA = {
  NODE_ENV: {
    type: 'string',
    enum: ['development', 'staging', 'production'],
    required: true
  },
  
  SCRAPE_DO_API_KEY: {
    type: 'string',
    required: true,
    sensitive: true,
    validation: /^[a-f0-9]{32}$/ // Exact 32 character hex string
  },
  
  OPENAI_API_KEY: {
    type: 'string',
    required: true,
    sensitive: true,
    validation: /^sk-[a-zA-Z0-9]{48,}$/
  },
  
  OPENROUTER_API_KEY: {
    type: 'string',
    required: true,
    sensitive: true,
    validation: /^sk-or-[a-zA-Z0-9-]{20,}$/
  },
  
  MAX_CONCURRENT_JOBS: {
    type: 'number',
    min: 1,
    max: 50,
    default: 5
  },
  
  JOB_TIMEOUT: {
    type: 'number',
    min: 30000,  // 30 seconds
    max: 600000, // 10 minutes
    default: 300000 // 5 minutes
  }
};
```

### 2. Environment Configuration Loader
```typescript
class EnvironmentConfigLoader {
  private config: EnvironmentConfiguration;
  private validationErrors: ValidationError[] = [];
  
  constructor() {
    this.loadConfiguration();
    this.validateConfiguration();
  }
  
  private loadConfiguration(): void {
    this.config = {
      // Core System
      NODE_ENV: process.env.NODE_ENV as any || 'development',
      PORT: parseInt(process.env.PORT || '3000'),
      SITE_URL: process.env.SITE_URL || 'http://localhost:3000',
      
      // Database
      SUPABASE_URL: process.env.SUPABASE_URL!,
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY!,
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY!,
      DATABASE_URL: process.env.DATABASE_URL!,
      
      // External APIs
      SCRAPE_DO_API_KEY: process.env.SCRAPE_DO_API_KEY!,
      SCRAPE_DO_BASE_URL: process.env.SCRAPE_DO_BASE_URL || 'https://api.scrape.do',
      OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
      OPENAI_ORGANIZATION: process.env.OPENAI_ORGANIZATION || '',
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY!,
      OPENROUTER_BASE_URL: process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
      
      // Job Processing
      JOB_QUEUE_REDIS_URL: process.env.JOB_QUEUE_REDIS_URL,
      JOB_CONCURRENCY: parseInt(process.env.JOB_CONCURRENCY || '3'),
      JOB_TIMEOUT: parseInt(process.env.JOB_TIMEOUT || '300000'),
      JOB_RETRY_ATTEMPTS: parseInt(process.env.JOB_RETRY_ATTEMPTS || '3'),
      
      // Security
      ADMIN_API_KEY: process.env.ADMIN_API_KEY!,
      JWT_SECRET: process.env.JWT_SECRET!,
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY!,
      
      // Monitoring & Logging
      LOG_LEVEL: process.env.LOG_LEVEL as any || 'info',
      SENTRY_DSN: process.env.SENTRY_DSN,
      ANALYTICS_API_KEY: process.env.ANALYTICS_API_KEY,
      
      // Feature Flags
      ENABLE_AI_GENERATION: process.env.ENABLE_AI_GENERATION !== 'false',
      ENABLE_BULK_PROCESSING: process.env.ENABLE_BULK_PROCESSING !== 'false',
      ENABLE_ADMIN_PANEL: process.env.ENABLE_ADMIN_PANEL !== 'false',
      ENABLE_RATE_LIMITING: process.env.ENABLE_RATE_LIMITING !== 'false',
      
      // Performance & Limits
      MAX_CONCURRENT_JOBS: parseInt(process.env.MAX_CONCURRENT_JOBS || '5'),
      MAX_CONTENT_SIZE: parseInt(process.env.MAX_CONTENT_SIZE || '1000000'),
      API_RATE_LIMIT: parseInt(process.env.API_RATE_LIMIT || '100'),
      CACHE_TTL: parseInt(process.env.CACHE_TTL || '3600')
    };
  }
  
  private validateConfiguration(): void {
    for (const [key, schema] of Object.entries(ENV_SCHEMA)) {
      const value = this.config[key as keyof EnvironmentConfiguration];
      
      // Check required fields
      if (schema.required && (value === undefined || value === null || value === '')) {
        this.validationErrors.push({
          field: key,
          error: 'Required field is missing',
          severity: 'critical'
        });
        continue;
      }
      
      // Type validation
      if (value !== undefined && typeof value !== schema.type) {
        this.validationErrors.push({
          field: key,
          error: `Expected ${schema.type}, got ${typeof value}`,
          severity: 'high'
        });
      }
      
      // Range validation for numbers
      if (schema.type === 'number' && typeof value === 'number') {
        if (schema.min !== undefined && value < schema.min) {
          this.validationErrors.push({
            field: key,
            error: `Value ${value} is below minimum ${schema.min}`,
            severity: 'medium'
          });
        }
        
        if (schema.max !== undefined && value > schema.max) {
          this.validationErrors.push({
            field: key,
            error: `Value ${value} is above maximum ${schema.max}`,
            severity: 'medium'
          });
        }
      }
      
      // Pattern validation
      if (schema.validation && typeof value === 'string') {
        if (!schema.validation.test(value)) {
          this.validationErrors.push({
            field: key,
            error: 'Value does not match required pattern',
            severity: 'high'
          });
        }
      }
      
      // Enum validation
      if (schema.enum && !schema.enum.includes(value)) {
        this.validationErrors.push({
          field: key,
          error: `Value must be one of: ${schema.enum.join(', ')}`,
          severity: 'high'
        });
      }
    }
    
    // Throw error if critical validation failures
    const criticalErrors = this.validationErrors.filter(e => e.severity === 'critical');
    if (criticalErrors.length > 0) {
      throw new Error(`Critical configuration errors: ${criticalErrors.map(e => e.error).join(', ')}`);
    }
  }
  
  getConfiguration(): EnvironmentConfiguration {
    return { ...this.config };
  }
  
  getValidationErrors(): ValidationError[] {
    return [...this.validationErrors];
  }
}
```

## Admin Panel Configuration

### 1. Admin Configuration Interface
```typescript
interface AdminPanelConfiguration {
  aiGeneration: {
    providers: {
      openai: {
        enabled: boolean;
        model: string;
        maxTokens: number;
        temperature: number;
        timeout: number;
      };
      openrouter: {
        enabled: boolean;
        model: string;
        maxTokens: number;
        temperature: number;
        implicitCaching: boolean;
        timeout: number;
      };
    };
    
    modelSelection: {
      strategy: 'auto' | 'manual' | 'cost_optimized' | 'quality_optimized';
      fallbackOrder: string[];
      costThreshold: number;
      qualityThreshold: number;
    };
    
    contentGeneration: {
      autoApproval: boolean;
      qualityThreshold: number;
      editorialReviewRequired: boolean;
      maxRetries: number;
      timeoutSeconds: number;
    };
  };
  
  webScraping: {
    scrapeDoConfig: {
      timeout: number;
      retryAttempts: number;
      useResidentialProxy: boolean;
      geoTargeting: string;
      customHeaders: boolean;
    };
    
    contentProcessing: {
      maxContentSize: number;
      optimizeForLLM: boolean;
      removeNavigation: boolean;
      removeFooter: boolean;
      minContentLength: number;
    };
    
    mediaCollection: {
      extractOGImages: boolean;
      extractFavicons: boolean;
      captureScreenshots: boolean;
      screenshotFallback: boolean;
      imageOptimization: boolean;
    };
  };
  
  jobProcessing: {
    concurrency: {
      maxConcurrentJobs: number;
      maxConcurrentBatches: number;
      maxJobsPerBatch: number;
    };
    
    timing: {
      delayBetweenRequests: number;
      batchProcessingDelay: number;
      jobTimeout: number;
      cleanupInterval: number;
    };
    
    errorHandling: {
      maxRetries: number;
      retryDelay: number;
      failureThreshold: number;
      autoRecovery: boolean;
    };
  };
  
  systemSettings: {
    performance: {
      cacheEnabled: boolean;
      cacheTTL: number;
      rateLimitEnabled: boolean;
      rateLimitRequests: number;
      rateLimitWindow: number;
    };
    
    monitoring: {
      healthCheckInterval: number;
      errorReportingEnabled: boolean;
      performanceMetrics: boolean;
      auditLogging: boolean;
    };
    
    security: {
      apiKeyRotationDays: number;
      sessionTimeout: number;
      maxLoginAttempts: number;
      requireTwoFactor: boolean;
    };
  };
}
```

### 2. Configuration Management Service
```typescript
class ConfigurationService {
  private environmentConfig: EnvironmentConfiguration;
  private adminConfig: AdminPanelConfiguration;
  private mergedConfig: SystemConfiguration;
  private configCache = new Map<string, any>();
  
  constructor() {
    this.loadConfigurations();
    this.mergeConfigurations();
    this.startConfigurationWatcher();
  }
  
  private async loadConfigurations(): Promise<void> {
    // Load environment configuration
    const envLoader = new EnvironmentConfigLoader();
    this.environmentConfig = envLoader.getConfiguration();
    
    // Load admin panel configuration from database
    this.adminConfig = await this.loadAdminConfiguration();
  }
  
  private async loadAdminConfiguration(): Promise<AdminPanelConfiguration> {
    try {
      const configData = await this.database.getAdminConfiguration();
      return this.validateAdminConfiguration(configData);
    } catch (error) {
      console.warn('Failed to load admin configuration, using defaults:', error.message);
      return this.getDefaultAdminConfiguration();
    }
  }
  
  private mergeConfigurations(): void {
    // Environment variables take precedence over admin panel settings
    this.mergedConfig = {
      ...this.getDefaultSystemConfiguration(),
      ...this.adminConfig,
      ...this.environmentConfig
    };
    
    // Apply configuration overrides
    this.applyConfigurationOverrides();
  }
  
  private applyConfigurationOverrides(): void {
    // Override admin settings with environment variables where applicable
    if (this.environmentConfig.MAX_CONCURRENT_JOBS) {
      this.mergedConfig.jobProcessing.concurrency.maxConcurrentJobs = 
        this.environmentConfig.MAX_CONCURRENT_JOBS;
    }
    
    if (this.environmentConfig.JOB_TIMEOUT) {
      this.mergedConfig.jobProcessing.timing.jobTimeout = 
        this.environmentConfig.JOB_TIMEOUT;
    }
    
    // Feature flag overrides
    if (!this.environmentConfig.ENABLE_AI_GENERATION) {
      this.mergedConfig.aiGeneration.providers.openai.enabled = false;
      this.mergedConfig.aiGeneration.providers.openrouter.enabled = false;
    }
  }
  
  // Configuration access methods
  get<T>(path: string): T {
    // Check cache first
    if (this.configCache.has(path)) {
      return this.configCache.get(path);
    }
    
    // Get value from merged configuration
    const value = this.getNestedValue(this.mergedConfig, path);
    
    // Cache the value
    this.configCache.set(path, value);
    
    return value;
  }
  
  async set(path: string, value: any): Promise<void> {
    // Validate the new value
    await this.validateConfigurationValue(path, value);
    
    // Update admin configuration in database
    await this.updateAdminConfiguration(path, value);
    
    // Update merged configuration
    this.setNestedValue(this.mergedConfig, path, value);
    
    // Clear cache
    this.configCache.delete(path);
    
    // Notify configuration change
    this.notifyConfigurationChange(path, value);
  }
  
  private async validateConfigurationValue(path: string, value: any): Promise<void> {
    const schema = this.getConfigurationSchema(path);
    
    if (!schema) {
      throw new Error(`No schema found for configuration path: ${path}`);
    }
    
    // Type validation
    if (typeof value !== schema.type) {
      throw new Error(`Invalid type for ${path}: expected ${schema.type}, got ${typeof value}`);
    }
    
    // Range validation
    if (schema.min !== undefined && value < schema.min) {
      throw new Error(`Value for ${path} is below minimum: ${schema.min}`);
    }
    
    if (schema.max !== undefined && value > schema.max) {
      throw new Error(`Value for ${path} is above maximum: ${schema.max}`);
    }
    
    // Custom validation
    if (schema.validator) {
      const isValid = await schema.validator(value);
      if (!isValid) {
        throw new Error(`Custom validation failed for ${path}`);
      }
    }
  }
  
  private startConfigurationWatcher(): void {
    // Watch for admin configuration changes
    setInterval(async () => {
      try {
        const latestAdminConfig = await this.loadAdminConfiguration();
        
        if (this.hasConfigurationChanged(this.adminConfig, latestAdminConfig)) {
          this.adminConfig = latestAdminConfig;
          this.mergeConfigurations();
          this.configCache.clear();
          
          console.log('Configuration updated from admin panel');
        }
      } catch (error) {
        console.error('Failed to check for configuration updates:', error.message);
      }
    }, 30000); // Check every 30 seconds
  }
  
  // Configuration export/import
  async exportConfiguration(): Promise<ConfigurationExport> {
    return {
      timestamp: new Date().toISOString(),
      environment: this.sanitizeConfiguration(this.environmentConfig),
      adminPanel: this.adminConfig,
      merged: this.sanitizeConfiguration(this.mergedConfig)
    };
  }
  
  async importConfiguration(configData: ConfigurationImport): Promise<void> {
    // Validate imported configuration
    await this.validateImportedConfiguration(configData);
    
    // Update admin configuration
    await this.updateAdminConfiguration('', configData.adminPanel);
    
    // Reload configurations
    await this.loadConfigurations();
    this.mergeConfigurations();
    this.configCache.clear();
  }
  
  private sanitizeConfiguration(config: any): any {
    // Remove sensitive information from configuration
    const sanitized = JSON.parse(JSON.stringify(config));
    
    const sensitiveFields = [
      'SCRAPE_DO_API_KEY',
      'OPENAI_API_KEY', 
      'OPENROUTER_API_KEY',
      'ADMIN_API_KEY',
      'JWT_SECRET',
      'ENCRYPTION_KEY',
      'DATABASE_URL'
    ];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    }
    
    return sanitized;
  }
}
```

## Security & Access Control

### 1. Configuration Security
```typescript
interface ConfigurationSecurity {
  encryption: {
    algorithm: 'aes-256-gcm';
    keyDerivation: 'pbkdf2';
    iterations: 100000;
    saltLength: 32;
  };
  
  accessControl: {
    roles: ['super_admin', 'admin', 'operator', 'viewer'];
    permissions: ConfigurationPermission[];
    auditLogging: boolean;
  };
  
  validation: {
    schemaValidation: boolean;
    sanitization: boolean;
    typeChecking: boolean;
    rangeValidation: boolean;
  };
}

class SecureConfigurationManager extends ConfigurationService {
  private encryptionService: EncryptionService;
  private accessControl: AccessControlService;
  private auditLogger: AuditLogger;
  
  constructor() {
    super();
    this.encryptionService = new EncryptionService();
    this.accessControl = new AccessControlService();
    this.auditLogger = new AuditLogger();
  }
  
  async setSecure(
    path: string, 
    value: any, 
    userId: string, 
    userRole: string
  ): Promise<void> {
    // Check permissions
    if (!this.accessControl.canModifyConfiguration(userRole, path)) {
      throw new Error(`Insufficient permissions to modify ${path}`);
    }
    
    // Encrypt sensitive values
    const encryptedValue = this.shouldEncrypt(path) 
      ? await this.encryptionService.encrypt(value)
      : value;
    
    // Set configuration
    await this.set(path, encryptedValue);
    
    // Log the change
    await this.auditLogger.logConfigurationChange({
      userId,
      userRole,
      path,
      action: 'update',
      timestamp: new Date(),
      sensitive: this.shouldEncrypt(path)
    });
  }
  
  async getSecure(path: string, userId: string, userRole: string): Promise<any> {
    // Check permissions
    if (!this.accessControl.canViewConfiguration(userRole, path)) {
      throw new Error(`Insufficient permissions to view ${path}`);
    }
    
    // Get configuration value
    const value = this.get(path);
    
    // Decrypt if necessary
    const decryptedValue = this.shouldEncrypt(path)
      ? await this.encryptionService.decrypt(value)
      : value;
    
    // Log access
    await this.auditLogger.logConfigurationAccess({
      userId,
      userRole,
      path,
      action: 'read',
      timestamp: new Date()
    });
    
    return decryptedValue;
  }
  
  private shouldEncrypt(path: string): boolean {
    const sensitivePatterns = [
      /api[_-]?key/i,
      /secret/i,
      /password/i,
      /token/i,
      /credential/i
    ];
    
    return sensitivePatterns.some(pattern => pattern.test(path));
  }
}
```

---

*This configuration management guide provides a comprehensive framework for managing all system configurations securely and efficiently. The implementation should ensure proper separation of concerns, security best practices, and operational flexibility while maintaining system reliability and auditability.*
