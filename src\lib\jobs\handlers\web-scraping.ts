import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, WebScrapingJobData } from '../types';
import puppeteer from 'puppeteer';

export class <PERSON><PERSON><PERSON>rapingHandler implements JobHandler {
  async handle(job: Job): Promise<any> {
    const data = job.data as WebScrapingJobData;
    
    let browser;
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      
      // Set user agent to avoid bot detection
      await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );

      // Set timeout
      const timeout = data.options?.timeout || parseInt(process.env.MAX_SCRAPE_TIMEOUT || '30000');
      page.setDefaultTimeout(timeout);

      // Navigate to the page
      await page.goto(data.url, { waitUntil: 'networkidle2' });

      // Wait for specific selector if provided
      if (data.options?.waitForSelector) {
        await page.waitForSelector(data.options.waitForSelector, { timeout: 10000 });
      }

      // Extract page data
      const scrapedData = await page.evaluate((options) => {
        const result: any = {
          title: document.title,
          url: window.location.href,
          meta: {},
          text: '',
          headings: [],
          links: [],
          images: [],
          favicon: null,
        };

        // Extract meta tags
        document.querySelectorAll('meta').forEach((meta) => {
          const name = meta.getAttribute('name') || meta.getAttribute('property');
          const content = meta.getAttribute('content');
          if (name && content) {
            result.meta[name] = content;
          }
        });

        // Extract main text content
        const mainContent = document.querySelector('main') || document.body;
        result.text = mainContent?.textContent?.trim().slice(0, 5000) || '';

        // Extract headings
        document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach((heading) => {
          result.headings.push({
            level: heading.tagName.toLowerCase(),
            text: heading.textContent?.trim(),
          });
        });

        // Extract links if requested
        if (options?.extractLinks) {
          document.querySelectorAll('a[href]').forEach((link) => {
            const href = link.getAttribute('href');
            const text = link.textContent?.trim();
            if (href && text) {
              result.links.push({ href, text });
            }
          });
        }

        // Extract images if requested
        if (options?.extractImages) {
          document.querySelectorAll('img[src]').forEach((img) => {
            const src = img.getAttribute('src');
            const alt = img.getAttribute('alt');
            if (src) {
              result.images.push({ src, alt });
            }
          });
        }

        // Extract favicon
        const faviconLink = document.querySelector('link[rel*="icon"]');
        if (faviconLink) {
          result.favicon = faviconLink.getAttribute('href');
        }

        // Extract pricing information
        const pricingKeywords = ['price', 'pricing', 'cost', 'plan', 'subscription', 'free', 'paid'];
        const pricingElements = Array.from(document.querySelectorAll('*')).filter((el) => {
          const text = el.textContent?.toLowerCase() || '';
          return pricingKeywords.some(keyword => text.includes(keyword));
        });

        result.pricing = pricingElements.slice(0, 10).map((el) => ({
          text: el.textContent?.trim(),
          tag: el.tagName.toLowerCase(),
        }));

        // Extract FAQ information
        const faqKeywords = ['faq', 'frequently asked', 'questions', 'help', 'support'];
        const faqElements = Array.from(document.querySelectorAll('*')).filter((el) => {
          const text = el.textContent?.toLowerCase() || '';
          return faqKeywords.some(keyword => text.includes(keyword));
        });

        result.faq = faqElements.slice(0, 10).map((el) => ({
          text: el.textContent?.trim(),
          tag: el.tagName.toLowerCase(),
        }));

        return result;
      }, data.options);

      // Take screenshot
      const screenshot = await page.screenshot({
        type: 'png',
        encoding: 'base64',
        fullPage: false,
        clip: { x: 0, y: 0, width: 1200, height: 800 },
      });

      return {
        success: true,
        data: scrapedData,
        screenshot: `data:image/png;base64,${screenshot}`,
        scrapedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Web scraping failed:', error);
      throw new Error(`Web scraping failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
}
