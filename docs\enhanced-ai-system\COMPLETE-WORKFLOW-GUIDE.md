# Complete Scraping and Content Generation Workflow Guide

## Overview

This document provides a comprehensive explanation of our end-to-end scraping and AI content generation workflow, optimized for cost efficiency while maintaining high-quality output. The workflow integrates Scrape.do API with OpenAI/OpenRouter for intelligent content generation.

## Workflow Architecture

```mermaid
graph TB
    A[URL Input] --> B[Initial Scraping Strategy]
    B --> C{Content Validation}
    C -->|Sufficient| D[Image Collection]
    C -->|Insufficient| E[Intelligent Re-scraping]
    E --> F{Re-scrape Success?}
    F -->|Yes| D
    F -->|No| G[Fallback Strategy]
    G --> D
    D --> H[Content Optimization]
    H --> I[AI Model Selection]
    I --> J[Content Generation]
    J --> K[Quality Assurance]
    K --> L[Final Output]
```

## Phase 1: Initial Scraping Strategy

### Cost-Optimized First Attempt
**Objective**: Minimize costs while gathering maximum information

**Configuration**:
```typescript
const initialScrapeConfig = {
  super: false,           // Datacenter proxy (1 credit)
  render: false,          // No browser rendering initially
  output: 'markdown',     // AI-ready format
  blockResources: true,   // Optimize performance
  timeout: 30000,         // Standard timeout
  geoCode: undefined      // No geo-targeting initially
};
```

**Decision Logic**:
1. **Always start with datacenter proxy** (1 credit vs 10 for residential)
2. **Avoid browser rendering initially** (prevents 5x cost multiplier)
3. **Use markdown output** for AI processing optimization
4. **Block resources** for faster, cheaper scraping

## Phase 2: Content Detection & Validation

### Client-Side Content Detection
**Indicators that trigger re-scraping**:

```typescript
const clientSideIndicators = [
  // Loading states
  'Loading...', 'Please wait', 'loading-spinner',
  
  // Empty containers
  '<div id="root"></div>', '<div id="app"></div>',
  
  // JavaScript frameworks
  /<script.*react.*<\/script>/i,
  /<script.*vue.*<\/script>/i,
  /<script.*angular.*<\/script>/i,
  
  // Content quality checks
  content.length < 500,           // Minimal content
  !/<h[1-6]/.test(content),      // No headings
  content.split('\n').length < 10 // Too few lines
];
```

### Validation Criteria
**Content must meet these standards**:
- **Minimum length**: 200 characters
- **Structure**: At least one heading (H1-H6)
- **Meaningful text**: 50+ words longer than 3 characters
- **No error indicators**: No "404", "access denied", etc.

## Phase 3: Intelligent Re-scraping Logic

### When to Re-scrape
**Triggers for enhanced scraping**:
1. Content validation fails
2. Client-side indicators detected
3. Minimal content extracted (<500 chars)
4. No structured elements found

### Re-scraping Configuration
```typescript
const enhancedScrapeConfig = {
  render: true,                    // Enable JavaScript (5x cost)
  waitUntil: 'networkidle2',      // Wait for network idle
  waitSelector: '.main-content, article, [role="main"]',
  customWait: 3000,               // Additional wait time
  blockResources: true,           // Keep costs controlled
  timeout: 45000,                 // Extended timeout
  super: false                    // Still use datacenter initially
};
```

### Cost Control During Re-scraping
**Maximum attempts**: 2 enhanced scrapes
**Credit thresholds**: 
- Minimum 50 credits for enhanced scraping
- Minimum 100 credits for residential proxy upgrade
**Fallback strategy**: Return best available content if limits reached

## Phase 4: Image Collection Strategy

### Favicon Priority (Always First)
**Extraction order**:
1. `<link rel="icon">` - Standard favicon
2. `<link rel="shortcut icon">` - Legacy favicon  
3. `<link rel="apple-touch-icon">` - Apple touch icon
4. `<link rel="apple-touch-icon-precomposed">` - Apple precomposed
5. `/favicon.ico` - Default fallback

**Cost**: No additional API calls (extracted from HTML content)

### OG Image Collection
**Primary sources** (no additional cost):
- `og:image` meta tags
- `twitter:image` meta tags  
- `facebook:image` meta tags

### Screenshot Fallback Strategy
**When to capture screenshots**:
- No OG images found in meta tags
- OG images are broken/inaccessible
- Explicit screenshot requirement

**Screenshot configuration**:
```typescript
const screenshotConfig = {
  screenShot: true,        // Standard viewport (NOT fullpage)
  width: 1200,            // Standard width
  height: 800,            // Standard height
  render: true,           // Required for screenshots
  blockResources: true,   // Optimize cost
  timeout: 30000          // Reasonable timeout
};
```

**Important**: Always use `screenShot: true` (viewport) NOT `fullScreenShot: true`

## Phase 5: AI Content Generation Pipeline

### Content Optimization for LLM
**Pre-processing steps**:
1. Remove navigation/footer elements
2. Clean HTML artifacts
3. Normalize heading structure
4. Limit content to token constraints
5. Preserve meaningful structure

### Intelligent Model Selection
**Decision matrix**:

| Content Size | Complexity | Selected Model | Reasoning |
|-------------|------------|----------------|-----------|
| >100K tokens | Complex | Gemini 2.5 Pro Preview | Large context + reasoning |
| <50K tokens | Any | GPT-4o | Speed optimization |
| Any | Medium | Gemini 2.5 Pro Preview | Cost + implicit caching |

### Content Generation Process
```typescript
const generationPipeline = {
  1: 'Content optimization and validation',
  2: 'Model selection based on content characteristics', 
  3: 'Context splitting if needed for token limits',
  4: 'Structured JSON generation with schema validation',
  5: 'Quality assurance and completeness check'
};
```

## Phase 6: Error Handling & Cost Control

### Credit Management
**Monitoring points**:
- Check credits before expensive operations
- Track daily/monthly usage limits
- Alert when approaching thresholds
- Implement circuit breakers for cost protection

### Fallback Strategies
**When primary scraping fails**:
1. **Proxy upgrade**: Datacenter → Residential (if credits available)
2. **Timeout extension**: Increase wait times for slow sites
3. **Simplified extraction**: Accept partial content if quality threshold met
4. **Manual intervention**: Flag for human review if automated methods fail

### Error Recovery
**Retry logic**:
- Maximum 3 total attempts per URL
- Exponential backoff: 2s, 5s, 10s delays
- Different configurations per attempt
- Cost tracking throughout process

## Phase 7: Quality Assurance

### Content Validation Checklist
**Before AI processing**:
- [ ] Minimum 200 characters
- [ ] Contains headings or structure
- [ ] No error page indicators
- [ ] Meaningful text content
- [ ] Proper markdown formatting

**After AI generation**:
- [ ] All required fields populated
- [ ] Content matches source material
- [ ] Proper JSON structure
- [ ] No hallucinated information
- [ ] Appropriate content length

### Quality Scoring
**Scoring criteria** (0-100 points):
- **Length** (30 points): Based on content volume
- **Structure** (25 points): Headings and organization
- **Diversity** (25 points): Unique words and concepts
- **Readability** (20 points): Sentence structure and flow

## Cost Optimization Summary

### Credit Usage Breakdown
**Minimum cost path**: 1 credit (datacenter + markdown)
**Enhanced path**: 5 credits (datacenter + browser + markdown)
**Maximum cost**: 50 credits (residential + browser + markdown)

### Optimization Strategies
1. **Start cheap**: Always begin with datacenter proxy
2. **Validate early**: Check content quality before expensive operations
3. **Smart rendering**: Only enable browser when necessary
4. **Batch processing**: Group similar URLs for efficiency
5. **Monitor usage**: Track credits and set limits

### Success Metrics
**Target performance**:
- **Success rate**: >90% content extraction
- **Average cost**: <5 credits per URL
- **Processing time**: <30 seconds per URL
- **Quality score**: >70 points average

---

*This workflow ensures optimal balance between cost efficiency and content quality, leveraging intelligent decision-making at each step to minimize Scrape.do costs while maximizing AI content generation success.*
