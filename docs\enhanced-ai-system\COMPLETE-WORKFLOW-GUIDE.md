# Complete Scraping and Content Generation Workflow Guide

## Overview

This document provides a comprehensive explanation of our end-to-end scraping and AI content generation workflow, optimized for cost efficiency while maintaining high-quality output. The workflow integrates Scrape.do API with OpenAI/OpenRouter for intelligent content generation.

## Workflow Architecture

```mermaid
graph TB
    A[URL Input] --> B[Initial Scraping Strategy]
    B --> C{Content Validation}
    C -->|Sufficient| D[Image Collection]
    C -->|Insufficient| E[Intelligent Re-scraping]
    E --> F{Re-scrape Success?}
    F -->|Yes| D
    F -->|No| G[Fallback Strategy]
    G --> D
    D --> H[Content Optimization]
    H --> I[AI Model Selection]
    I --> J[Content Generation]
    J --> K[Quality Assurance]
    K --> L[Final Output]
```

## Phase 1: Initial Scraping Strategy

### Cost-Optimized First Attempt
**Objective**: Minimize costs while gathering maximum information

**Configuration**:
```typescript
const initialScrapeConfig = {
  super: false,           // Datacenter proxy (1 credit)
  render: false,          // No browser rendering initially
  output: 'markdown',     // AI-ready format
  blockResources: true,   // Optimize performance
  timeout: 30000,         // Standard timeout
  geoCode: undefined      // No geo-targeting initially
};
```

**Decision Logic**:
1. **Always start with datacenter proxy** (1 credit vs 10 for residential)
2. **Avoid browser rendering initially** (prevents 5x cost multiplier)
3. **Use markdown output** for AI processing optimization
4. **Block resources** for faster, cheaper scraping

## Phase 2: Content Detection & Validation

### Intelligent Content Analysis
**Enhanced detection handles complex scenarios**:

```typescript
// Scenario-based content analysis instead of simple indicators
const contentScenarios = {
  // Scenario 1: Meta + Loading + Full Content = KEEP (don't re-scrape)
  'meta_loading_content_sufficient': {
    hasMetaTags: true,
    hasLoadingIndicators: true,
    hasSubstantialContent: true,
    action: 'keep_content'
  },

  // Scenario 2: Meta + Loading + No Content = ENHANCE (re-scrape)
  'meta_loading_no_content': {
    hasMetaTags: true,
    hasLoadingIndicators: true,
    hasSubstantialContent: false,
    action: 'enhance_scraping'
  },

  // Scenario 3: Meta Only = ENHANCE (re-scrape)
  'meta_only_minimal': {
    hasMetaTags: true,
    hasSubstantialContent: false,
    hasStructure: false,
    action: 'enhance_scraping'
  },

  // Scenario 4: Mixed content with low ratio = ENHANCE
  'low_content_ratio': {
    contentRatio: '<0.15',
    action: 'enhance_scraping'
  }
};

// Intelligent analysis factors:
const analysisFactors = {
  metaTagCount: 'Substantial meta tag presence (>5 tags)',
  loadingIndicators: 'Loading text, spinners, or placeholders',
  substantialContent: '>100 meaningful words, multiple sentences',
  contentStructure: 'Headings, paragraphs, lists, or links',
  contentRatio: 'Clean content / total content ratio',
  confidence: 'Overall confidence score (0-100)'
};
```

### Enhanced Validation Criteria
**Multi-factor content analysis**:

1. **Meta Tag Analysis**: Count and quality of meta tags
2. **Loading Indicator Detection**: Identify dynamic loading elements
3. **Content Extraction**: Separate actual content from meta/loading noise
4. **Structure Analysis**: Headings, paragraphs, lists, links
5. **Content Ratio Calculation**: Meaningful content vs total content
6. **Scenario Classification**: Determine appropriate action based on pattern

**Decision Matrix**:
| Meta Tags | Loading | Substantial Content | Structure | Action |
|-----------|---------|-------------------|-----------|---------|
| ✅ | ✅ | ✅ | ✅ | **KEEP** - Good content with progressive loading |
| ✅ | ✅ | ❌ | ❌ | **ENHANCE** - Client-side rendered, needs JS |
| ✅ | ❌ | ❌ | ❌ | **ENHANCE** - Meta only, likely needs rendering |
| ❌ | ✅ | ❌ | ❌ | **ENHANCE** - Loading without content |
| Any | Any | ❌ | ❌ | **ENHANCE** - Insufficient content regardless |

## Phase 3: Intelligent Re-scraping Logic

### When to Re-scrape
**Triggers for enhanced scraping**:
1. Content validation fails
2. Client-side indicators detected
3. Minimal content extracted (<500 chars)
4. No structured elements found

### Re-scraping Configuration
```typescript
const enhancedScrapeConfig = {
  render: true,                    // Enable JavaScript (5x cost)
  waitUntil: 'networkidle2',      // Wait for network idle
  waitSelector: '.main-content, article, [role="main"]',
  customWait: 3000,               // Additional wait time
  blockResources: true,           // Keep costs controlled
  timeout: 45000,                 // Extended timeout
  super: false                    // Still use datacenter initially
};
```

### Cost Control During Re-scraping
**Maximum attempts**: 2 enhanced scrapes
**Credit thresholds**: 
- Minimum 50 credits for enhanced scraping
- Minimum 100 credits for residential proxy upgrade
**Fallback strategy**: Return best available content if limits reached

## Phase 4: Image Collection Strategy

### Favicon Priority (Always First)
**Extraction order**:
1. `<link rel="icon">` - Standard favicon
2. `<link rel="shortcut icon">` - Legacy favicon  
3. `<link rel="apple-touch-icon">` - Apple touch icon
4. `<link rel="apple-touch-icon-precomposed">` - Apple precomposed
5. `/favicon.ico` - Default fallback

**Cost**: No additional API calls (extracted from HTML content)

### OG Image Collection
**Primary sources** (no additional cost):
- `og:image` meta tags
- `twitter:image` meta tags  
- `facebook:image` meta tags

### Screenshot Fallback Strategy
**When to capture screenshots**:
- No OG images found in meta tags
- OG images are broken/inaccessible
- Explicit screenshot requirement

**Screenshot configuration**:
```typescript
const screenshotConfig = {
  screenShot: true,        // Standard viewport (NOT fullpage)
  width: 1200,            // Standard width
  height: 800,            // Standard height
  render: true,           // Required for screenshots
  blockResources: true,   // Optimize cost
  timeout: 30000          // Reasonable timeout
};
```

**Important**: Always use `screenShot: true` (viewport) NOT `fullScreenShot: true`

## Phase 5: Multi-Page Content Discovery and Integration

### Configurable Page Detection
**Objective**: Discover and collect additional content pages based on configuration

**Page Types Supported**:
- **Pricing**: `/pricing`, `/plans`, `/subscription` (High Priority, Required)
- **FAQ**: `/faq`, `/help`, `/support` (Medium Priority, Optional)
- **Features**: `/features`, `/capabilities` (High Priority, Required)
- **About**: `/about`, `/company`, `/story` (Low Priority, Optional)

### Discovery Methods
**1. Main Page Content Search** (No additional cost):
```typescript
// Check if content exists in main page using CSS selectors
const pricingInMain = findContentInMainPage(mainContent, [
  '.pricing', '.plans', '[class*="price"]'
]);
```

**2. Navigation Link Detection**:
```typescript
// Extract navigation links matching patterns
const navLinks = findNavigationLinks(mainContent, [
  '/pricing', '/plans', '/subscription'
]);
```

**3. URL Pattern Construction**:
```typescript
// Construct likely URLs as fallback
const constructedUrls = [
  `${baseUrl}/pricing`,
  `${baseUrl}/plans`,
  `${baseUrl}/features`
];
```

### Intelligent Scraping Decision
**Decision Matrix**:

| Credits Available | Page Priority | Confidence | Action |
|------------------|---------------|------------|---------|
| >200 | High | >70% | **SCRAPE NOW** |
| >100 | High | >50% | **SCRAPE NOW** |
| >100 | Medium/Low | >80% | **SCRAPE NOW** |
| <100 | Any | Any | **QUEUE FOR LATER** |
| <50 | Any | Any | **SKIP** |

### Queue Management
**For Later Processing**:
```typescript
interface QueuedPage {
  pageType: 'pricing' | 'faq' | 'features' | 'about';
  url: string;
  priority: 'high' | 'medium' | 'low';
  estimatedCredits: number;
  reason: 'insufficient_credits' | 'low_confidence' | 'low_priority';
  queuedAt: string;
}
```

## Phase 6: AI Content Generation Pipeline

### Content Optimization for LLM
**Pre-processing steps**:
1. Remove navigation/footer elements
2. Clean HTML artifacts
3. Normalize heading structure
4. Limit content to token constraints
5. Preserve meaningful structure

### Intelligent Model Selection
**Decision matrix**:

| Content Size | Complexity | Selected Model | Reasoning |
|-------------|------------|----------------|-----------|
| >100K tokens | Complex | Gemini 2.5 Pro Preview | Large context + reasoning |
| <50K tokens | Any | GPT-4o | Speed optimization |
| Any | Medium | Gemini 2.5 Pro Preview | Cost + implicit caching |

### Multi-Page Content Processing
**Token Management Strategy**:
```typescript
// Priority order for content inclusion (within token limits)
const contentPriority = [
  'mainPage',     // Always include (highest priority)
  'features',     // Critical for understanding tool capabilities
  'pricing',      // Essential for directory categorization
  'faq',          // Helpful for user questions
  'about'         // Lowest priority, include if space allows
];

// Token allocation strategy
const tokenAllocation = {
  systemPrompt: 1000,      // Reserved for instructions
  mainContent: 60000,      // ~15K tokens for main page
  additionalPages: 40000,  // ~10K tokens for additional pages
  responseBuffer: 8192     // Reserved for structured response
};
```

### Structured Output Configuration
**Provider-Specific Setup**:
```typescript
// OpenAI Structured Outputs (strict mode)
const openaiConfig = {
  response_format: {
    type: "json_schema",
    json_schema: {
      name: "ai_tool_content",
      strict: true,
      schema: AI_TOOL_CONTENT_SCHEMA
    }
  },
  max_tokens: 16384 // GPT-4o output limit
};

// OpenRouter/Gemini JSON mode
const openrouterConfig = {
  response_format: {
    type: "json_object",
    schema: AI_TOOL_CONTENT_SCHEMA
  },
  max_tokens: 8192 // Gemini 2.5 Pro Preview output limit
};
```

### Content Generation Process
```typescript
const enhancedGenerationPipeline = {
  1: 'Multi-page content discovery and collection',
  2: 'Intelligent page prioritization based on credits/confidence',
  3: 'Content optimization and token management',
  4: 'Model selection with output token consideration',
  5: 'Structured JSON generation with schema validation',
  6: 'Quality assurance and completeness check',
  7: 'Queue management for deferred pages'
};
```

## Phase 6: Error Handling & Cost Control

### Credit Management
**Monitoring points**:
- Check credits before expensive operations
- Track daily/monthly usage limits
- Alert when approaching thresholds
- Implement circuit breakers for cost protection

### Fallback Strategies
**When primary scraping fails**:
1. **Proxy upgrade**: Datacenter → Residential (if credits available)
2. **Timeout extension**: Increase wait times for slow sites
3. **Simplified extraction**: Accept partial content if quality threshold met
4. **Manual intervention**: Flag for human review if automated methods fail

### Error Recovery
**Retry logic**:
- Maximum 3 total attempts per URL
- Exponential backoff: 2s, 5s, 10s delays
- Different configurations per attempt
- Cost tracking throughout process

## Phase 7: Quality Assurance

### Content Validation Checklist
**Before AI processing**:
- [ ] Minimum 200 characters
- [ ] Contains headings or structure
- [ ] No error page indicators
- [ ] Meaningful text content
- [ ] Proper markdown formatting

**After AI generation**:
- [ ] All required fields populated
- [ ] Content matches source material
- [ ] Proper JSON structure
- [ ] No hallucinated information
- [ ] Appropriate content length

### Quality Scoring
**Scoring criteria** (0-100 points):
- **Length** (30 points): Based on content volume
- **Structure** (25 points): Headings and organization
- **Diversity** (25 points): Unique words and concepts
- **Readability** (20 points): Sentence structure and flow

## Cost Optimization Summary

### Credit Usage Breakdown
**Minimum cost path**: 1 credit (datacenter + markdown)
**Enhanced path**: 5 credits (datacenter + browser + markdown)
**Maximum cost**: 50 credits (residential + browser + markdown)

### Optimization Strategies
1. **Start cheap**: Always begin with datacenter proxy
2. **Validate early**: Check content quality before expensive operations
3. **Smart rendering**: Only enable browser when necessary
4. **Batch processing**: Group similar URLs for efficiency
5. **Monitor usage**: Track credits and set limits

### Success Metrics
**Target performance**:
- **Success rate**: >90% content extraction
- **Average cost**: <5 credits per URL
- **Processing time**: <30 seconds per URL
- **Quality score**: >70 points average

---

*This workflow ensures optimal balance between cost efficiency and content quality, leveraging intelligent decision-making at each step to minimize Scrape.do costs while maximizing AI content generation success.*
