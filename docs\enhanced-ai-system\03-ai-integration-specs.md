# AI Integration Specifications - OpenAI & OpenRouter

## Overview

This document defines the comprehensive AI integration specifications for the enhanced content generation system, covering both OpenAI and OpenRouter APIs with intelligent model selection, context management, and prompt optimization.

## API Configuration

### OpenAI Configuration
```typescript
const OPENAI_CONFIG = {
  apiKey: process.env.OPENAI_API_KEY,
  baseUrl: 'https://api.openai.com/v1',
  model: 'gpt-4o',
  maxTokens: 128000, // Context window limit
  temperature: 0.7,
  timeout: 60000,
  retryAttempts: 3
};
```

### Enhanced AI Configuration with Multi-Page Support
```typescript
interface AIModelConfig {
  provider: 'openai' | 'openrouter';
  model: string;
  maxInputTokens: number;
  maxOutputTokens: number; // Proper output token limits
  temperature: number;
  timeout: number;
  retryAttempts: number;
  structuredOutput: boolean; // Enable structured outputs
  multiPageSupport: boolean; // Handle multi-page content
}

const OPENROUTER_CONFIG: AIModelConfig = {
  provider: 'openrouter',
  model: 'google/gemini-2.5-pro-preview',
  maxInputTokens: 1048576, // ~1M tokens input context
  maxOutputTokens: 8192, // Gemini 2.5 Pro Preview output limit
  temperature: 0.7,
  timeout: 120000,
  retryAttempts: 3,
  structuredOutput: true,
  multiPageSupport: true,
  extraHeaders: {
    'HTTP-Referer': process.env.SITE_URL || 'https://aidude.com',
    'X-Title': 'AI Dude Directory'
  }
};

const OPENAI_CONFIG: AIModelConfig = {
  provider: 'openai',
  model: 'gpt-4o',
  maxInputTokens: 128000, // GPT-4o input limit
  maxOutputTokens: 16384, // GPT-4o output limit
  temperature: 0.7,
  timeout: 60000,
  retryAttempts: 3,
  structuredOutput: true,
  multiPageSupport: true
};

// Model-specific output token limits (from OpenRouter docs)
const MODEL_OUTPUT_LIMITS = {
  'google/gemini-2.5-pro-preview': 8192,
  'google/gemini-pro-1.5': 8192,
  'openai/gpt-4o': 16384,
  'openai/gpt-4o-mini': 16384,
  'anthropic/claude-3.5-sonnet': 8192,
  'meta-llama/llama-3.1-405b': 4096
};
```

## Model Selection Strategy

### 1. Intelligent Model Selection
```typescript
interface ModelSelectionCriteria {
  contentSize: number; // Token count of input content
  complexity: 'simple' | 'medium' | 'complex';
  priority: 'speed' | 'quality' | 'cost';
  features: string[]; // Required features (caching, multimodal, etc.)
}

function selectOptimalModel(criteria: ModelSelectionCriteria): ModelConfig {
  const { contentSize, complexity, priority } = criteria;
  
  // Use Gemini 2.5 Pro Preview for large content or complex tasks
  if (contentSize > 100000 || complexity === 'complex') {
    return {
      provider: 'openrouter',
      model: 'google/gemini-2.5-pro-preview',
      maxTokens: 1048576,
      reasoning: 'Large context window required with advanced reasoning'
    };
  }

  // Use GPT-4o for speed-critical tasks
  if (priority === 'speed' && contentSize < 50000) {
    return {
      provider: 'openai',
      model: 'gpt-4o',
      maxTokens: 128000,
      reasoning: 'Optimized for speed with sufficient context'
    };
  }

  // Default to OpenRouter for cost optimization with implicit caching
  return {
    provider: 'openrouter',
    model: 'google/gemini-2.5-pro-preview',
    maxTokens: 1048576,
    reasoning: 'Cost-effective with large context window and implicit caching'
  };
}
```

### 2. Context Window Management
```typescript
function calculateTokenCount(text: string): number {
  // Approximate token calculation (1 token ≈ 4 characters for English)
  return Math.ceil(text.length / 4);
}

function splitContentForModel(content: string, modelConfig: ModelConfig): string[] {
  const tokenCount = calculateTokenCount(content);
  const maxTokensPerChunk = Math.floor(modelConfig.maxTokens * 0.8); // Reserve 20% for response
  
  if (tokenCount <= maxTokensPerChunk) {
    return [content];
  }
  
  // Split content by logical sections
  const sections = content.split(/\n(?=#{1,3}\s)/); // Split on headers
  const chunks = [];
  let currentChunk = '';
  
  for (const section of sections) {
    const sectionTokens = calculateTokenCount(section);
    const currentTokens = calculateTokenCount(currentChunk);
    
    if (currentTokens + sectionTokens > maxTokensPerChunk && currentChunk) {
      chunks.push(currentChunk.trim());
      currentChunk = section;
    } else {
      currentChunk += (currentChunk ? '\n' : '') + section;
    }
  }
  
  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }
  
  return chunks;
}
```

## Content Generation System

### 1. System Prompt Configuration
```typescript
const SYSTEM_PROMPTS = {
  toolAnalysis: `You are an expert AI tool analyst with deep knowledge of the AI industry. Your task is to analyze AI tools and generate comprehensive, accurate content that matches our database schema.

CRITICAL REQUIREMENTS:
- Generate content in JSON format matching the exact database schema
- Use an irreverent, humorous tone similar to ThePornDude style
- Be factual but entertaining in descriptions
- Include specific technical details and use cases
- Generate 3-8 features, 3-10 pros/cons, 3-6 Q&As
- Keep descriptions under 500 characters, detailed descriptions 150-300 words
- Create engaging haikus and relevant hashtags
- Classify pricing accurately: Free, Paid, Freemium, or Open Source

SCHEMA REQUIREMENTS:
{
  "detailed_description": "string (150-300 words)",
  "features": ["array of 3-8 feature strings"],
  "pricing": {
    "type": "Free|Paid|Freemium|Open Source",
    "plans": [{"name": "string", "price": "string", "features": ["array"]}]
  },
  "pros_and_cons": {
    "pros": ["array of 3-10 pros"],
    "cons": ["array of 3-10 cons"]
  },
  "haiku": {
    "lines": ["line1", "line2", "line3"],
    "theme": "string"
  },
  "hashtags": ["array of 5-10 relevant hashtags"],
  "social_links": {
    "twitter": "url or null",
    "linkedin": "url or null",
    "github": "url or null"
  }
}`,

  contentCompletion: `You are continuing content generation for an AI tool. Previous context has been provided. Generate the remaining content sections while maintaining consistency with the established tone and style.

CONTINUATION REQUIREMENTS:
- Maintain the same irreverent, humorous tone
- Ensure consistency with previously generated content
- Complete any missing sections from the schema
- Do not repeat information from previous chunks
- Focus on the specific sections requested`,

  contentValidation: `You are a content quality validator. Review the generated content for accuracy, completeness, and adherence to our style guidelines.

VALIDATION CRITERIA:
- Schema compliance (all required fields present)
- Content quality (engaging, informative, accurate)
- Tone consistency (irreverent but professional)
- Technical accuracy (features and capabilities)
- Pricing accuracy (correct classification and details)`
};
```

### 2. Multi-Prompt Processing
```typescript
async function generateContentWithMultiplePrompts(
  scrapedContent: string,
  toolUrl: string,
  modelConfig: ModelConfig
): Promise<GeneratedContent> {
  const contentChunks = splitContentForModel(scrapedContent, modelConfig);
  
  if (contentChunks.length === 1) {
    // Single prompt processing
    return await generateSinglePromptContent(contentChunks[0], toolUrl, modelConfig);
  }
  
  // Multi-prompt processing with completion waiting
  const results = [];
  let accumulatedContext = '';
  
  for (let i = 0; i < contentChunks.length; i++) {
    const chunk = contentChunks[i];
    const isFirstChunk = i === 0;
    const isLastChunk = i === contentChunks.length - 1;
    
    const prompt = buildMultiPrompt(chunk, {
      isFirstChunk,
      isLastChunk,
      chunkIndex: i,
      totalChunks: contentChunks.length,
      accumulatedContext,
      toolUrl
    });
    
    const result = await callAIProvider(prompt, modelConfig);
    results.push(result);
    
    // Accumulate context for next chunk
    accumulatedContext += `\n\nPrevious chunk result: ${JSON.stringify(result)}`;
    
    // Wait for completion signal if not last chunk
    if (!isLastChunk) {
      await waitForCompletionSignal(result);
    }
  }
  
  // Combine results from all chunks
  return combineMultiPromptResults(results);
}

function buildMultiPrompt(
  chunk: string,
  context: MultiPromptContext
): string {
  const { isFirstChunk, isLastChunk, chunkIndex, totalChunks, accumulatedContext, toolUrl } = context;
  
  let prompt = '';
  
  if (isFirstChunk) {
    prompt = `${SYSTEM_PROMPTS.toolAnalysis}

MULTI-CHUNK PROCESSING: This is chunk ${chunkIndex + 1} of ${totalChunks}.
Generate initial content sections and prepare for continuation.

Tool URL: ${toolUrl}
Scraped Content Chunk:
${chunk}

Generate the following sections for this chunk:
- detailed_description (if sufficient information available)
- features (partial list, will be completed in subsequent chunks)
- pricing (if pricing information is in this chunk)

Respond with JSON and include a "continuation_needed" field indicating what sections need completion.`;
  } else if (isLastChunk) {
    prompt = `${SYSTEM_PROMPTS.contentCompletion}

FINAL CHUNK: Complete all remaining sections and finalize the content.

Previous Context:
${accumulatedContext}

Final Content Chunk:
${chunk}

Complete the content generation with all remaining sections:
- Complete features list
- pros_and_cons
- haiku
- hashtags
- social_links
- Any missing sections

Provide the final complete JSON response.`;
  } else {
    prompt = `${SYSTEM_PROMPTS.contentCompletion}

MIDDLE CHUNK: Continue content generation with this additional information.

Previous Context:
${accumulatedContext}

Additional Content Chunk:
${chunk}

Continue building the content, focusing on:
- Additional features
- More detailed information
- Pricing details (if found)

Respond with JSON and indicate what sections still need completion.`;
  }
  
  return prompt;
}
```

### 3. AI Provider Integration
```typescript
class AIContentGenerator {
  private openaiClient: OpenAI;
  private openrouterClient: OpenAI; // OpenRouter uses OpenAI-compatible API
  
  constructor() {
    this.openaiClient = new OpenAI({
      apiKey: OPENAI_CONFIG.apiKey,
      baseURL: OPENAI_CONFIG.baseUrl
    });
    
    this.openrouterClient = new OpenAI({
      apiKey: OPENROUTER_CONFIG.apiKey,
      baseURL: OPENROUTER_CONFIG.baseUrl
    });
  }
  
  async generateContent(
    scrapedContent: string,
    toolUrl: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    try {
      // Select optimal model
      const modelConfig = selectOptimalModel({
        contentSize: calculateTokenCount(scrapedContent),
        complexity: options.complexity || 'medium',
        priority: options.priority || 'quality',
        features: options.features || []
      });
      
      // Generate content
      const result = await generateContentWithMultiplePrompts(
        scrapedContent,
        toolUrl,
        modelConfig
      );
      
      // Validate generated content
      const validation = await this.validateGeneratedContent(result);
      
      return {
        success: true,
        content: result,
        validation,
        modelUsed: modelConfig,
        tokenUsage: result.tokenUsage,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  private async callAIProvider(
    prompt: string,
    modelConfig: ModelConfig
  ): Promise<AIResponse> {
    const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;
    
    const requestConfig = {
      model: modelConfig.model,
      messages: [
        { role: 'system', content: SYSTEM_PROMPTS.toolAnalysis },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 4000, // Reserve tokens for response
      response_format: { type: 'json_object' }
    };
    
    // Add OpenRouter-specific features
    if (modelConfig.provider === 'openrouter') {
      requestConfig.extra_headers = {
        'HTTP-Referer': process.env.SITE_URL || 'https://aidude.com',
        'X-Title': 'AI Dude Directory'
      };

      // Implicit caching is automatic for Gemini 2.5 Pro Preview
      // No manual cache_prompt configuration needed
    }
    
    const response = await client.chat.completions.create(requestConfig);
    
    return {
      content: response.choices[0].message.content,
      tokenUsage: response.usage,
      model: response.model,
      finishReason: response.choices[0].finish_reason
    };
  }
  
  private async validateGeneratedContent(content: GeneratedContent): Promise<ValidationResult> {
    const issues = [];
    
    // Check required fields
    const requiredFields = ['detailed_description', 'features', 'pricing', 'pros_and_cons'];
    for (const field of requiredFields) {
      if (!content[field]) {
        issues.push(`Missing required field: ${field}`);
      }
    }
    
    // Validate content length
    if (content.detailed_description && content.detailed_description.length > 300) {
      issues.push('Detailed description exceeds 300 words');
    }
    
    // Validate features count
    if (content.features && (content.features.length < 3 || content.features.length > 8)) {
      issues.push('Features count should be between 3-8');
    }
    
    // Validate pricing type
    const validPricingTypes = ['Free', 'Paid', 'Freemium', 'Open Source'];
    if (content.pricing && !validPricingTypes.includes(content.pricing.type)) {
      issues.push('Invalid pricing type');
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      score: Math.max(0, 100 - (issues.length * 10)) // Quality score
    };
  }
}
```

## Error Handling & Recovery

### 1. Comprehensive Error Management
```typescript
class AIErrorHandler {
  static async handleAIError(error: any, context: ErrorContext): Promise<ErrorResult> {
    const errorResult: ErrorResult = {
      success: false,
      error: error.message,
      retryable: false,
      context,
      timestamp: new Date().toISOString()
    };
    
    // OpenAI specific errors
    if (error.code === 'rate_limit_exceeded') {
      errorResult.retryable = true;
      errorResult.retryAfter = 60; // Wait 1 minute
      errorResult.suggestion = 'Switch to OpenRouter or implement exponential backoff';
    } else if (error.code === 'context_length_exceeded') {
      errorResult.suggestion = 'Split content into smaller chunks or use Gemini 2.5 Pro';
    } else if (error.code === 'invalid_request_error') {
      errorResult.error = 'Invalid request format or parameters';
    }
    
    // OpenRouter specific errors
    if (error.status === 402) {
      errorResult.error = 'Insufficient credits on OpenRouter';
      errorResult.suggestion = 'Check OpenRouter balance or switch to OpenAI';
    } else if (error.status === 429) {
      errorResult.retryable = true;
      errorResult.retryAfter = 30;
    }
    
    // Content validation errors
    if (error.type === 'validation_error') {
      errorResult.suggestion = 'Review system prompt and content requirements';
    }
    
    return errorResult;
  }
  
  static async retryWithFallback(
    operation: () => Promise<any>,
    maxRetries: number = 3
  ): Promise<any> {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        const errorResult = await this.handleAIError(error, { attempt, maxRetries });
        
        if (!errorResult.retryable) {
          break;
        }
        
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
}
```

### 2. Prompt Caching Optimization (OpenRouter)
```typescript
class PromptCacheManager {
  private static cacheKeys = new Map<string, string>();

  static generateCacheKey(systemPrompt: string, toolDomain: string): string {
    const key = `${toolDomain}_${hashString(systemPrompt)}`;
    this.cacheKeys.set(key, systemPrompt);
    return key;
  }

  static async generateWithCaching(
    prompt: string,
    modelConfig: ModelConfig,
    cacheKey?: string
  ): Promise<AIResponse> {
    const requestConfig = {
      model: modelConfig.model,
      messages: [
        { role: 'system', content: SYSTEM_PROMPTS.toolAnalysis },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 4000,
      response_format: { type: 'json_object' }
    };

    // Implicit caching is enabled automatically for Gemini 2.5 Pro Preview
    // No manual cache configuration needed - OpenRouter handles this automatically

    return await this.callProvider(requestConfig, modelConfig);
  }
}

### 3. Fallback Strategies
```typescript
async function generateContentWithFallback(
  scrapedContent: string,
  toolUrl: string
): Promise<GenerationResult> {
  const strategies = [
    // Strategy 1: OpenRouter with Gemini 2.5 Pro Preview and implicit caching
    () => generateWithOpenRouter(scrapedContent, toolUrl, { implicitCaching: true }),

    // Strategy 2: OpenAI with content splitting
    () => generateWithOpenAI(scrapedContent, toolUrl, { splitContent: true }),

    // Strategy 3: Simplified generation with basic content
    () => generateBasicContent(scrapedContent, toolUrl)
  ];

  for (const [index, strategy] of strategies.entries()) {
    try {
      const result = await AIErrorHandler.retryWithFallback(strategy);

      if (result.success) {
        result.strategyUsed = index + 1;
        return result;
      }
    } catch (error) {
      console.warn(`Strategy ${index + 1} failed:`, error.message);

      if (index === strategies.length - 1) {
        throw error; // All strategies failed
      }
    }
  }

  throw new Error('All generation strategies failed');
}
```

## Integration with Job System

### 1. Job Processing Integration
```typescript
export class AIGenerationJobHandler {
  private generator: AIContentGenerator;
  
  constructor() {
    this.generator = new AIContentGenerator();
  }
  
  async processAIGenerationJob(job: AIGenerationJob): Promise<JobResult> {
    const { scrapedContent, toolUrl, options = {} } = job.data;
    
    try {
      await updateJobStatus(job.id, 'processing', { 
        step: 'ai_generation',
        progress: 10 
      });
      
      // Generate content with fallback strategies
      const result = await generateContentWithFallback(scrapedContent, toolUrl);
      
      await updateJobStatus(job.id, 'processing', { 
        step: 'validation',
        progress: 80 
      });
      
      // Additional validation
      if (!result.validation.isValid) {
        console.warn(`Content validation issues:`, result.validation.issues);
      }
      
      await updateJobStatus(job.id, 'completed', { 
        result,
        progress: 100 
      });
      
      return {
        success: true,
        data: result.content,
        metadata: {
          modelUsed: result.modelUsed,
          tokenUsage: result.tokenUsage,
          validationScore: result.validation.score
        }
      };
      
    } catch (error) {
      await updateJobStatus(job.id, 'failed', { 
        error: error.message,
        progress: 0 
      });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}
```

## Multi-Page Content Integration and Structured Output

### 1. Multi-Page Content Schema
```typescript
interface MultiPageContent {
  mainPage: {
    url: string;
    content: string;
    extractedAt: string;
    quality: ContentQuality;
  };
  additionalPages: {
    pricing?: {
      url: string;
      content: string;
      extractedAt: string;
      foundMethod: 'navigation' | 'pattern' | 'content';
      confidence: number;
    };
    faq?: {
      url: string;
      content: string;
      extractedAt: string;
      foundMethod: 'navigation' | 'pattern' | 'content';
      confidence: number;
    };
    features?: {
      url: string;
      content: string;
      extractedAt: string;
      foundMethod: 'navigation' | 'pattern' | 'content';
      confidence: number;
    };
    about?: {
      url: string;
      content: string;
      extractedAt: string;
      foundMethod: 'navigation' | 'pattern' | 'content';
      confidence: number;
    };
  };
  queuedForLater: Array<{
    pageType: 'pricing' | 'faq' | 'features' | 'about';
    url: string;
    priority: 'high' | 'medium' | 'low';
    estimatedCredits: number;
    reason: string;
  }>;
}
```

### 2. Structured Output Configuration with Token Management
```typescript
// Model-specific output token limits (from OpenRouter/OpenAI docs)
const MODEL_OUTPUT_LIMITS = {
  'google/gemini-2.5-pro-preview': 8192,
  'google/gemini-pro-1.5': 8192,
  'openai/gpt-4o': 16384,
  'openai/gpt-4o-mini': 16384,
  'anthropic/claude-3.5-sonnet': 8192,
  'meta-llama/llama-3.1-405b': 4096
};

// Structured Output Schema (JSON Schema format)
const AI_TOOL_CONTENT_SCHEMA = {
  type: "object",
  properties: {
    name: { type: "string", maxLength: 100 },
    description: { type: "string", maxLength: 500 },
    detailedDescription: { type: "string", minLength: 150, maxLength: 300 },
    category: { type: "string" },
    subcategory: { type: "string" },
    pricingType: {
      type: "string",
      enum: ["Free", "Freemium", "Paid", "Open Source"]
    },
    features: {
      type: "array",
      items: { type: "string", maxLength: 100 },
      minItems: 3,
      maxItems: 8
    },
    pros: {
      type: "array",
      items: { type: "string", maxLength: 100 },
      minItems: 3,
      maxItems: 10
    },
    cons: {
      type: "array",
      items: { type: "string", maxLength: 100 },
      minItems: 3,
      maxItems: 10
    },
    pricing: {
      type: "array",
      items: {
        type: "object",
        properties: {
          name: { type: "string", maxLength: 50 },
          price: { type: "string", maxLength: 50 },
          features: {
            type: "array",
            items: { type: "string", maxLength: 100 }
          }
        },
        required: ["name", "price"]
      }
    },
    faq: {
      type: "array",
      items: {
        type: "object",
        properties: {
          question: { type: "string", maxLength: 200 },
          answer: { type: "string", maxLength: 500 }
        },
        required: ["question", "answer"]
      },
      minItems: 3,
      maxItems: 6
    },
    tags: {
      type: "array",
      items: { type: "string", maxLength: 30 },
      maxItems: 10
    },
    tooltip: { type: "string", maxLength: 150 },
    haiku: { type: "string", maxLength: 200 }
  },
  required: ["name", "description", "detailedDescription", "category", "pricingType", "features", "pros", "cons"],
  additionalProperties: false
};

class StructuredOutputManager {
  getOutputConfiguration(provider: string, model: string): any {
    const outputLimit = MODEL_OUTPUT_LIMITS[model] || 4096;

    if (provider === 'openai') {
      // OpenAI Structured Outputs (strict mode)
      return {
        response_format: {
          type: "json_schema",
          json_schema: {
            name: "ai_tool_content",
            strict: true,
            schema: AI_TOOL_CONTENT_SCHEMA
          }
        },
        max_tokens: Math.min(outputLimit, 16384)
      };
    } else {
      // OpenRouter/Gemini JSON mode
      return {
        response_format: {
          type: "json_object",
          schema: AI_TOOL_CONTENT_SCHEMA
        },
        max_tokens: Math.min(outputLimit, 8192)
      };
    }
  }
}
```

---

*This enhanced AI integration specification provides comprehensive support for multi-page content processing, proper token management, and structured outputs. The system intelligently handles content from multiple pages while respecting model token limits and ensuring consistent, structured responses across different AI providers.*
