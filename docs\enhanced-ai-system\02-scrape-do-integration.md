# Scrape.do API Integration Guide

## Overview

This document provides comprehensive integration specifications for the scrape.do API, which serves as the primary web scraping service for the enhanced AI-powered content generation system. The integration focuses on extracting structured data in markdown format optimized for LLM consumption.

## API Configuration

### Authentication
```typescript
const SCRAPE_DO_CONFIG = {
  apiKey: process.env.SCRAPE_DO_API_KEY, // 8e7e405ff81145c4afe447610ddb9a7f785f494dddc
  baseUrl: 'https://api.scrape.do',
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 2000 // 2 seconds
};
```

### Enhanced Request Structure
```typescript
interface ScrapeRequest {
  // Required Parameters
  token: string;
  url: string; // Must be URL encoded for API mode

  // Proxy Configuration
  super?: boolean; // Use Residential & Mobile proxy networks (10 credits)
  geoCode?: string; // Country targeting (us, uk, de, fr, etc.)
  regionalGeoCode?: string; // Continental targeting (na, eu, as, etc.)
  sessionId?: number; // Sticky sessions for consistent IP

  // Browser & Rendering
  render?: boolean; // Enable headless browser (5x cost multiplier)
  device?: 'desktop' | 'mobile' | 'tablet'; // Device type simulation
  width?: number; // Browser width (default: 1920)
  height?: number; // Browser height (default: 1080)
  waitUntil?: 'domcontentloaded' | 'load' | 'networkidle0' | 'networkidle2';
  customWait?: number; // Additional wait time in milliseconds
  waitSelector?: string; // CSS selector to wait for
  blockResources?: boolean; // Block CSS/images for performance (default: true)

  // Headers & Cookies
  customHeaders?: boolean; // Handle all request headers
  extraHeaders?: boolean; // Add/modify specific headers
  forwardHeaders?: boolean; // Forward your headers to target
  setCookies?: string; // Set cookies for the request

  // Output & Response
  output?: 'raw' | 'markdown'; // Output format
  screenShot?: boolean; // Capture screenshot
  fullScreenShot?: boolean; // Full page screenshot
  particularScreenShot?: string; // Screenshot specific element
  returnJSON?: boolean; // Return network requests as JSON
  showFrames?: boolean; // Include iframe content (requires render=true)
  showWebsocketRequests?: boolean; // Show websocket requests
  transparentResponse?: boolean; // Return raw target response
  pureCookies?: boolean; // Return original Set-Cookie headers

  // Request Control
  timeout?: number; // Request timeout (default: 60000ms)
  retryTimeout?: number; // Retry timeout (default: 15000ms)
  disableRetry?: boolean; // Disable retry mechanism
  disableRedirection?: boolean; // Disable following redirects
  callback?: string; // Webhook URL for async results

  // Browser Automation
  playWithBrowser?: Array<{
    Action: 'Click' | 'Wait' | 'Execute' | 'Scroll' | 'Type';
    Selector?: string;
    Timeout?: number;
    Execute?: string;
    Text?: string;
  }>;
}
```

## Core Integration Functions

### 1. Enhanced Page Scraping with Cost Optimization
```typescript
interface ScrapeOptions {
  // Proxy Configuration
  useResidentialProxy?: boolean; // Use super=true for residential/mobile
  geoTargeting?: string; // Country code for geo-targeting
  stickySession?: number; // Session ID for consistent IP

  // Browser Configuration
  enableJSRendering?: boolean; // Enable headless browser
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  waitCondition?: 'domcontentloaded' | 'load' | 'networkidle0' | 'networkidle2';
  customWaitTime?: number; // Additional wait in milliseconds
  waitForSelector?: string; // CSS selector to wait for

  // Output Configuration
  outputFormat?: 'raw' | 'markdown';
  captureScreenshot?: boolean;
  fullPageScreenshot?: boolean;
  includeNetworkRequests?: boolean;

  // Performance Optimization
  blockResources?: boolean; // Block CSS/images for faster scraping
  timeout?: number;
  retryTimeout?: number;
}

interface ContentAnalysis {
  hasMetaTags: boolean;           // Substantial meta tag presence
  hasLoadingIndicators: boolean;  // Loading text/spinners detected
  hasSubstantialContent: boolean; // Meaningful content beyond meta/loading
  hasStructure: boolean;          // Headings, paragraphs, lists
  contentRatio: number;           // Clean content / total content ratio
  needsEnhancedScraping: boolean; // Final decision for re-scraping
  confidence: number;             // Confidence score (0-100)
  scenario?: string;              // Detected scenario for debugging
}

async function scrapePage(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
  // Build query parameters based on options
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url // URL will be encoded by URLSearchParams
  });

  // Add optional parameters
  if (options.useResidentialProxy) params.set('super', 'true');
  if (options.geoTargeting) params.set('geoCode', options.geoTargeting);
  if (options.stickySession) params.set('sessionId', options.stickySession.toString());
  if (options.enableJSRendering) params.set('render', 'true');
  if (options.deviceType) params.set('device', options.deviceType);
  if (options.waitCondition) params.set('waitUntil', options.waitCondition);
  if (options.customWaitTime) params.set('customWait', options.customWaitTime.toString());
  if (options.waitForSelector) params.set('waitSelector', options.waitForSelector);
  if (options.outputFormat) params.set('output', options.outputFormat);
  if (options.captureScreenshot) params.set('screenShot', 'true');
  if (options.fullPageScreenshot) params.set('fullScreenShot', 'true');
  if (options.includeNetworkRequests) params.set('returnJSON', 'true');
  if (options.blockResources !== undefined) params.set('blockResources', options.blockResources.toString());
  if (options.timeout) params.set('timeout', options.timeout.toString());
  if (options.retryTimeout) params.set('retryTimeout', options.retryTimeout.toString());

  const requestUrl = `${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`;

  try {
    const response = await fetch(requestUrl, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br' // Enable compression for better performance
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const content = await response.text();
    const metadata = extractMetadata(response.headers);

    return {
      success: true,
      content,
      metadata,
      timestamp: new Date().toISOString(),
      costInfo: {
        creditsUsed: calculateCreditsUsed(options),
        requestType: getRequestType(options)
      }
    };
  } catch (error) {
    return handleScrapeError(error, url, options);
  }
}

// Helper function to calculate credits used
function calculateCreditsUsed(options: ScrapeOptions): number {
  let credits = 1; // Base cost for datacenter proxy

  if (options.useResidentialProxy) {
    credits = 10; // Residential/Mobile proxy base cost
  }

  if (options.enableJSRendering) {
    credits *= 5; // 5x multiplier for headless browser
  }

  return credits;
}

// Helper function to determine request type
function getRequestType(options: ScrapeOptions): string {
  if (options.useResidentialProxy && options.enableJSRendering) {
    return 'Residential + Browser';
  } else if (options.useResidentialProxy) {
    return 'Residential Proxy';
  } else if (options.enableJSRendering) {
    return 'Datacenter + Browser';
  } else {
    return 'Datacenter Proxy';
  }
}
```

### 2. Open Graph Image Extraction
```typescript
async function extractOGImages(url: string): Promise<MediaAsset[]> {
  const scrapeResult = await scrapePage(url, {
    customHeaders: true,
    render: true
  });

  if (!scrapeResult.success) {
    throw new Error(`Failed to scrape ${url}: ${scrapeResult.error}`);
  }

  const ogImages = [];
  const content = scrapeResult.content;

  // Extract OG images from scraped content
  const ogImageRegex = /<meta\s+property=["']og:image["']\s+content=["']([^"']+)["']/gi;
  const twitterImageRegex = /<meta\s+name=["']twitter:image["']\s+content=["']([^"']+)["']/gi;
  const facebookImageRegex = /<meta\s+property=["']fb:image["']\s+content=["']([^"']+)["']/gi;

  let match;
  
  // Extract og:image
  while ((match = ogImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'og:image',
      url: resolveUrl(match[1], url),
      priority: 1
    });
  }

  // Extract twitter:image
  while ((match = twitterImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'twitter:image',
      url: resolveUrl(match[1], url),
      priority: 2
    });
  }

  // Extract facebook image
  while ((match = facebookImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'facebook:image',
      url: resolveUrl(match[1], url),
      priority: 3
    });
  }

  return ogImages.sort((a, b) => a.priority - b.priority);
}
```

### 3. Advanced Browser Automation
```typescript
interface BrowserAction {
  Action: 'Click' | 'Wait' | 'Execute' | 'Scroll' | 'Type';
  Selector?: string; // CSS selector for Click, Scroll, Type actions
  Timeout?: number; // Wait time in milliseconds for Wait action
  Execute?: string; // JavaScript code for Execute action
  Text?: string; // Text to type for Type action
}

async function automatedScraping(url: string, actions: BrowserAction[]): Promise<ScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    render: 'true', // Required for browser automation
    playWithBrowser: JSON.stringify(actions)
  });

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);
  return await response.text();
}

// Example: Automated form interaction
const loginAndScrape = await automatedScraping('https://example.com/login', [
  { Action: 'Click', Selector: '#login-button' },
  { Action: 'Wait', Timeout: 2000 },
  { Action: 'Type', Selector: '#username', Text: '<EMAIL>' },
  { Action: 'Type', Selector: '#password', Text: 'password123' },
  { Action: 'Click', Selector: '#submit' },
  { Action: 'Wait', Timeout: 3000 },
  { Action: 'Execute', Execute: 'document.querySelector("#dashboard").innerHTML' }
]);
```

### 4. Enhanced Screenshot Capture
```typescript
interface ScreenshotOptions {
  fullPage?: boolean; // Capture full page screenshot
  elementSelector?: string; // Capture specific element
  width?: number; // Browser width (default: 1920)
  height?: number; // Browser height (default: 1080)
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  waitForSelector?: string; // Wait for element before screenshot
  customWait?: number; // Additional wait time
}

async function captureScreenshot(url: string, options: ScreenshotOptions = {}): Promise<ScreenshotResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    render: 'true' // Required for screenshots
  });

  // Configure screenshot type
  if (options.fullPage) {
    params.set('fullScreenShot', 'true');
  } else if (options.elementSelector) {
    params.set('particularScreenShot', options.elementSelector);
  } else {
    params.set('screenShot', 'true');
  }

  // Configure browser settings
  if (options.width) params.set('width', options.width.toString());
  if (options.height) params.set('height', options.height.toString());
  if (options.deviceType) params.set('device', options.deviceType);
  if (options.waitForSelector) params.set('waitSelector', options.waitForSelector);
  if (options.customWait) params.set('customWait', options.customWait.toString());

  try {
    const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Screenshot capture failed: ${response.status}`);
    }

    const screenshotData = await response.arrayBuffer();

    return {
      success: true,
      screenshot: Buffer.from(screenshotData).toString('base64'),
      metadata: {
        width: options.width || 1920,
        height: options.height || 1080,
        fullPage: options.fullPage || false,
        capturedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}
```

### 5. Advanced Proxy Configuration
```typescript
interface ProxyConfiguration {
  useResidentialProxy?: boolean; // Use super=true for residential/mobile networks
  geoTargeting?: string; // Country code (us, uk, de, fr, ca, au, etc.)
  regionalTargeting?: string; // Regional targeting (na, eu, as, etc.)
  stickySession?: number; // Session ID for consistent IP across requests
  customHeaders?: boolean; // Handle all request headers
  forwardHeaders?: boolean; // Forward your headers to target
  extraHeaders?: Record<string, string>; // Additional headers to set
}

async function scrapeWithAdvancedProxy(url: string, proxyConfig: ProxyConfiguration): Promise<ScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url
  });

  // Configure proxy settings
  if (proxyConfig.useResidentialProxy) {
    params.set('super', 'true'); // 10 credits vs 1 for datacenter
  }

  if (proxyConfig.geoTargeting) {
    params.set('geoCode', proxyConfig.geoTargeting);
  }

  if (proxyConfig.regionalTargeting) {
    params.set('regionalGeoCode', proxyConfig.regionalTargeting);
  }

  if (proxyConfig.stickySession) {
    params.set('sessionId', proxyConfig.stickySession.toString());
  }

  if (proxyConfig.customHeaders) {
    params.set('customHeaders', 'true');
  }

  if (proxyConfig.forwardHeaders) {
    params.set('forwardHeaders', 'true');
  }

  if (proxyConfig.extraHeaders) {
    params.set('extraHeaders', 'true');
    // Note: Extra headers would be sent in request body for POST requests
  }

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);
  return await response.text();
}

// Example: Scrape with US residential proxy and sticky session
const result = await scrapeWithAdvancedProxy('https://example.com', {
  useResidentialProxy: true,
  geoTargeting: 'us',
  stickySession: 12345,
  customHeaders: true
});
```

### 6. Cost Optimization Strategies
```typescript
interface CostOptimizationConfig {
  preferDatacenter?: boolean; // Use datacenter proxy when possible (1 credit vs 10)
  blockResources?: boolean; // Block CSS/images for faster scraping
  useMarkdownOutput?: boolean; // Get structured output for AI processing
  batchRequests?: boolean; // Process multiple URLs efficiently
  enableRetry?: boolean; // Enable smart retry mechanism
}

class CostOptimizedScraper {
  private config: CostOptimizationConfig;

  constructor(config: CostOptimizationConfig = {}) {
    this.config = {
      preferDatacenter: true,
      blockResources: true,
      useMarkdownOutput: true,
      batchRequests: true,
      enableRetry: true,
      ...config
    };
  }

  async scrapeWithOptimization(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
    const params = new URLSearchParams({
      token: SCRAPE_DO_CONFIG.apiKey,
      url: url
    });

    // Apply cost optimizations
    if (this.config.blockResources) {
      params.set('blockResources', 'true');
    }

    if (this.config.useMarkdownOutput) {
      params.set('output', 'markdown');
    }

    // Only use residential proxy if specifically needed
    if (options.requiresResidentialProxy) {
      params.set('super', 'true');
    }

    // Enable browser only if JavaScript is required
    if (options.requiresJavaScript) {
      params.set('render', 'true');
    }

    if (!this.config.enableRetry) {
      params.set('disableRetry', 'true');
    }

    try {
      const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();
      const creditsUsed = this.calculateCreditsUsed(options);

      return {
        success: true,
        content,
        metadata: {
          creditsUsed,
          proxyType: options.requiresResidentialProxy ? 'residential' : 'datacenter',
          browserEnabled: options.requiresJavaScript || false
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  private calculateCreditsUsed(options: ScrapeOptions): number {
    let credits = 1; // Base datacenter cost

    if (options.requiresResidentialProxy) {
      credits = 10; // Residential proxy cost
    }

    if (options.requiresJavaScript) {
      credits *= 5; // Browser rendering multiplier
    }

    return credits;
  }
}
```

### 7. Favicon Extraction
```typescript
async function extractFavicon(url: string): Promise<FaviconResult> {
  const scrapeResult = await scrapePage(url, {
    customHeaders: true,
    render: true
  });

  if (!scrapeResult.success) {
    throw new Error(`Failed to scrape ${url}: ${scrapeResult.error}`);
  }

  const content = scrapeResult.content;
  const faviconUrls = [];

  // Extract favicon URLs from various sources
  const faviconRegexes = [
    /<link\s+rel=["']icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']shortcut icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']apple-touch-icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']apple-touch-icon-precomposed["']\s+href=["']([^"']+)["']/gi
  ];

  faviconRegexes.forEach(regex => {
    let match;
    while ((match = regex.exec(content)) !== null) {
      faviconUrls.push(resolveUrl(match[1], url));
    }
  });

  // Add default favicon.ico if no favicon found
  if (faviconUrls.length === 0) {
    const baseUrl = new URL(url).origin;
    faviconUrls.push(`${baseUrl}/favicon.ico`);
  }

  return {
    faviconUrls: [...new Set(faviconUrls)], // Remove duplicates
    primaryFavicon: faviconUrls[0] || null
  };
}
```

## Complete Scraping and Content Generation Workflow

### 1. Intelligent Scraping Strategy
```typescript
interface ScrapingStrategy {
  initialAttempt: 'datacenter-basic';    // 1 credit - try first
  fallbackProxy: 'residential';          // 10 credits - if blocked
  renderingTrigger: 'content-detection'; // 5x multiplier - if needed
  costThreshold: number;                  // Credit limit checks
}

class IntelligentScraper {
  async scrapeWithStrategy(url: string): Promise<ScrapeResult> {
    // Step 1: Cost-optimized initial attempt
    let result = await this.basicScrape(url, {
      super: false,        // Datacenter proxy (1 credit)
      render: false,       // No browser rendering
      output: 'markdown',  // AI-ready format
      blockResources: true // Optimize performance
    });

    // Step 2: Content validation and re-scraping decision
    if (this.detectClientSideContent(result.content)) {
      result = await this.enhancedScrape(url, {
        render: true,                    // Enable JavaScript (5x cost)
        waitUntil: 'networkidle2',      // Wait for content to load
        waitSelector: '.main-content, article, [role="main"]',
        customWait: 3000,               // Additional wait time
        blockResources: true            // Keep costs controlled
      });
    }

    return result;
  }

  private detectClientSideContent(content: string, url?: string): boolean {
    // Perform intelligent content analysis instead of simple indicator checking
    const analysis = this.analyzeContentQuality(content);

    // Additional check: Known sites that require browser rendering
    const requiresBrowserRendering = this.checkKnownBrowserRequiredSites(url);

    // Only trigger re-scraping if content is genuinely insufficient OR known to require browser
    return analysis.needsEnhancedScraping || requiresBrowserRendering;
  }

  private checkKnownBrowserRequiredSites(url?: string): boolean {
    if (!url) return false;

    const browserRequiredPatterns = [
      // AI/ML Platforms (like claude.ai)
      /claude\.ai/i,
      /openai\.com/i,
      /chat\.openai\.com/i,
      /bard\.google\.com/i,
      /character\.ai/i,

      // Modern SaaS platforms
      /notion\.so/i,
      /figma\.com/i,
      /canva\.com/i,
      /miro\.com/i,
      /airtable\.com/i,

      // React/Vue/Angular heavy sites
      /vercel\.app/i,
      /netlify\.app/i,
      /\.vercel\.com/i,

      // Common SPA frameworks indicators
      /app\./i,  // app.example.com subdomains often use SPAs
      /dashboard\./i, // dashboard.example.com

      // Sites with known client-side rendering
      /discord\.com/i,
      /slack\.com/i,
      /teams\.microsoft\.com/i
    ];

    const requiresBrowser = browserRequiredPatterns.some(pattern => pattern.test(url));

    if (requiresBrowser) {
      console.log(`URL ${url} matches known browser-required pattern - will use enhanced scraping`);
    }

    return requiresBrowser;
  }

  private analyzeContentQuality(content: string): ContentAnalysis {
    const analysis: ContentAnalysis = {
      hasMetaTags: false,
      hasLoadingIndicators: false,
      hasSubstantialContent: false,
      hasStructure: false,
      contentRatio: 0,
      needsEnhancedScraping: false,
      confidence: 0
    };

    // 1. Check for meta tags presence
    analysis.hasMetaTags = this.hasMetaTags(content);

    // 2. Check for loading indicators
    analysis.hasLoadingIndicators = this.hasLoadingIndicators(content);

    // 3. Analyze substantial content (excluding meta tags and loading indicators)
    const cleanContent = this.extractMainContent(content);
    analysis.hasSubstantialContent = this.hasSubstantialContent(cleanContent);

    // 4. Check for content structure
    analysis.hasStructure = this.hasContentStructure(cleanContent);

    // 5. Calculate content-to-noise ratio
    analysis.contentRatio = this.calculateContentRatio(content, cleanContent);

    // 6. Make intelligent decision based on analysis
    analysis.needsEnhancedScraping = this.shouldEnhanceScraping(analysis);
    analysis.confidence = this.calculateConfidence(analysis);

    return analysis;
  }

  private hasMetaTags(content: string): boolean {
    const metaTagCount = (content.match(/<meta\s+/gi) || []).length;
    return metaTagCount > 5; // Substantial meta tag presence
  }

  private hasLoadingIndicators(content: string): boolean {
    const loadingPatterns = [
      /loading\.{3}/gi,
      /please\s+wait/gi,
      /loading-spinner/gi,
      /loader/gi,
      /<div[^>]*class="[^"]*loading[^"]*"/gi,
      /<div[^>]*id="[^"]*loading[^"]*"/gi
    ];

    return loadingPatterns.some(pattern => pattern.test(content));
  }

  private extractMainContent(content: string): string {
    // Remove meta tags, scripts, styles, and loading indicators
    let cleanContent = content
      .replace(/<meta[^>]*>/gi, '')
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<link[^>]*>/gi, '')
      .replace(/loading\.{3}|please\s+wait|loading-spinner/gi, '')
      .replace(/<div[^>]*class="[^"]*loading[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')
      .replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi, '')
      .replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

    // Remove excessive whitespace
    cleanContent = cleanContent.replace(/\s+/g, ' ').trim();

    return cleanContent;
  }

  private hasSubstantialContent(cleanContent: string): boolean {
    const wordCount = cleanContent.split(/\s+/).filter(word => word.length > 2).length;
    const meaningfulTextRegex = /[a-zA-Z]{10,}/;
    const sentenceCount = cleanContent.split(/[.!?]+/).length;

    return (
      wordCount > 100 &&                    // At least 100 meaningful words
      meaningfulTextRegex.test(cleanContent) && // Has substantial text blocks
      sentenceCount > 5                     // Has multiple sentences
    );
  }

  private hasContentStructure(cleanContent: string): boolean {
    const hasHeadings = /<h[1-6]|^#{1,6}\s/m.test(cleanContent);
    const hasParagraphs = cleanContent.split(/\n\s*\n/).length > 3;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(cleanContent);
    const hasLinks = /<a\s+href|^\[.*\]\(/m.test(cleanContent);

    // Content has structure if it has headings plus at least one other structural element
    return hasHeadings && (hasParagraphs || hasLists || hasLinks);
  }

  private calculateContentRatio(fullContent: string, cleanContent: string): number {
    if (fullContent.length === 0) return 0;
    return cleanContent.length / fullContent.length;
  }

  private shouldEnhanceScraping(analysis: ContentAnalysis): boolean {
    // Scenario 1: Meta + Loading + Full Content = DON'T re-scrape
    if (analysis.hasMetaTags && analysis.hasLoadingIndicators && analysis.hasSubstantialContent) {
      analysis.scenario = 'meta_loading_content_sufficient';
      console.log('Content analysis: Meta tags + Loading indicators + Substantial content detected - KEEPING content');
      return false;
    }

    // Scenario 2: Meta + Loading + No Content = DO re-scrape
    if (analysis.hasMetaTags && analysis.hasLoadingIndicators && !analysis.hasSubstantialContent) {
      analysis.scenario = 'meta_loading_no_content';
      console.log('Content analysis: Meta tags + Loading indicators but no substantial content - ENHANCING scraping');
      return true;
    }

    // Scenario 3: Meta Only (minimal content) = DO re-scrape
    if (analysis.hasMetaTags && !analysis.hasSubstantialContent && !analysis.hasStructure) {
      analysis.scenario = 'meta_only_minimal';
      console.log('Content analysis: Only meta tags detected, minimal content - ENHANCING scraping');
      return true;
    }

    // Scenario 4: Loading indicators with insufficient content = DO re-scrape
    if (analysis.hasLoadingIndicators && !analysis.hasSubstantialContent) {
      analysis.scenario = 'loading_insufficient_content';
      console.log('Content analysis: Loading indicators with insufficient content - ENHANCING scraping');
      return true;
    }

    // Scenario 5: No substantial content regardless of other factors = DO re-scrape
    if (!analysis.hasSubstantialContent) {
      analysis.scenario = 'no_substantial_content';
      console.log('Content analysis: No substantial content detected - ENHANCING scraping');
      return true;
    }

    // Scenario 6: Low content ratio (mostly noise/meta) = DO re-scrape
    if (analysis.contentRatio < 0.15) {
      analysis.scenario = 'low_content_ratio';
      console.log(`Content analysis: Low content ratio (${analysis.contentRatio.toFixed(2)}) - ENHANCING scraping`);
      return true;
    }

    // Scenario 7: Has content but no structure (might be incomplete) = CONDITIONAL re-scrape
    if (analysis.hasSubstantialContent && !analysis.hasStructure && analysis.contentRatio < 0.3) {
      analysis.scenario = 'content_no_structure';
      console.log('Content analysis: Content present but lacks structure - ENHANCING scraping');
      return true;
    }

    // Content appears sufficient
    analysis.scenario = 'content_sufficient';
    console.log(`Content analysis: Content sufficient (ratio: ${analysis.contentRatio.toFixed(2)}, confidence: ${analysis.confidence}) - KEEPING content`);
    return false;
  }

  private calculateConfidence(analysis: ContentAnalysis): number {
    let confidence = 0;

    if (analysis.hasSubstantialContent) confidence += 40;
    if (analysis.hasStructure) confidence += 30;
    if (analysis.contentRatio > 0.2) confidence += 20;
    if (!analysis.hasLoadingIndicators) confidence += 10;

    return Math.min(100, confidence);
  }
}

// Enhanced scraping with automatic retry logic (based on claude.ai example)
async function scrapeWithIntelligentAnalysis(url: string): Promise<ScrapeResult> {
  console.log(`Starting intelligent scraping for: ${url}`);

  // Step 1: Initial cost-optimized scrape (1 credit)
  const initialResult = await scrapePage(url, {
    super: false,        // Datacenter proxy
    render: false,       // No browser rendering initially
    output: 'markdown',  // AI-ready format
    blockResources: true // Optimize performance
  });

  if (!initialResult.success) {
    console.log('Initial scrape failed, will retry with enhanced settings');
    return await enhancedScrapeWithBrowser(url);
  }

  // Step 2: Analyze content quality
  const analysis = analyzeContentQuality(initialResult.content);

  console.log('Content Analysis Results:', {
    url: url,
    scenario: analysis.scenario,
    hasMetaTags: analysis.hasMetaTags,
    hasLoadingIndicators: analysis.hasLoadingIndicators,
    hasSubstantialContent: analysis.hasSubstantialContent,
    hasStructure: analysis.hasStructure,
    contentRatio: analysis.contentRatio.toFixed(3),
    confidence: analysis.confidence,
    contentLength: initialResult.content.length,
    needsEnhancedScraping: analysis.needsEnhancedScraping
  });

  // Step 3: Make intelligent decision
  if (analysis.needsEnhancedScraping) {
    console.log(`Triggering enhanced scraping due to: ${analysis.scenario}`);
    console.log(`Example: claude.ai requires render=true for full content`);
    return await enhancedScrapeWithBrowser(url);
  }

  console.log('Content quality sufficient, using initial scrape result');
  return initialResult;
}

// Enhanced scraping with browser rendering (like claude.ai example)
async function enhancedScrapeWithBrowser(url: string): Promise<ScrapeResult> {
  console.log(`Enhanced scraping with browser rendering for: ${url}`);

  const enhancedResult = await scrapePage(url, {
    super: false,                    // Keep datacenter proxy initially (cost control)
    render: true,                    // Enable JavaScript execution (5x cost)
    output: 'markdown',              // AI-ready format
    waitUntil: 'networkidle0',      // Wait for network to be completely idle
    customWait: 3000,               // Additional 3-second wait for dynamic content
    blockResources: true,           // Block CSS/images for performance
    timeout: 45000,                 // Extended timeout for JS rendering
    width: 1920,                    // Standard desktop viewport
    height: 1080                    // Standard desktop viewport
  });

  // If still insufficient and we have credits, try residential proxy
  if (enhancedResult.success) {
    const reAnalysis = analyzeContentQuality(enhancedResult.content);

    if (!reAnalysis.needsEnhancedScraping) {
      console.log(`Enhanced scraping successful: ${reAnalysis.scenario}`);
      return enhancedResult;
    }
  }

  // Last resort: Residential proxy + browser rendering (expensive but effective)
  console.log(`Attempting residential proxy for difficult site: ${url}`);

  const finalResult = await scrapePage(url, {
    super: true,                     // Residential proxy (10x base cost)
    render: true,                    // Browser rendering (5x multiplier)
    output: 'markdown',              // AI-ready format
    waitUntil: 'networkidle2',      // Less strict network idle condition
    customWait: 5000,               // Longer wait for difficult sites
    blockResources: true,           // Keep costs controlled
    timeout: 60000,                 // Extended timeout
    geoCode: 'us'                   // US-based residential proxy
  });

  return finalResult.success ? finalResult : enhancedResult; // Return best result
}
```

### Real-World Scenario Examples

```typescript
// Example scenarios and expected behavior:

const scenarioExamples = {
  // Scenario 1: Meta + Loading + Full Content (DON'T re-scrape)
  goodContentWithLoading: `
    <meta name="description" content="AI tool for content generation">
    <meta property="og:title" content="ContentAI - AI Writing Assistant">
    <meta property="og:description" content="Generate high-quality content with AI">

    <h1>ContentAI - AI Writing Assistant</h1>
    <p>ContentAI is a powerful AI-powered writing assistant that helps you create high-quality content...</p>
    <div class="loading-spinner">Loading additional features...</div>
    <h2>Key Features</h2>
    <ul>
      <li>AI-powered content generation</li>
      <li>Multiple writing styles</li>
      <li>Real-time collaboration</li>
    </ul>
    <p>Our advanced AI algorithms analyze your requirements and generate content that matches your style...</p>
  `,
  // Expected: needsEnhancedScraping = false (substantial content present despite loading indicator)

  // Scenario 2: Meta + Loading + No Content (DO re-scrape)
  metaLoadingNoContent: `
    <meta name="description" content="AI tool for content generation">
    <meta property="og:title" content="ContentAI - AI Writing Assistant">
    <meta property="og:description" content="Generate high-quality content with AI">

    <div id="root">Loading...</div>
    <div class="loading-spinner">Please wait while we load the application...</div>
    <script src="app.js"></script>
  `,
  // Expected: needsEnhancedScraping = true (only meta tags and loading, no actual content)

  // Scenario 3: Meta Only (DO re-scrape)
  metaOnly: `
    <meta name="description" content="AI tool for content generation">
    <meta property="og:title" content="ContentAI - AI Writing Assistant">
    <meta property="og:description" content="Generate high-quality content with AI">
    <meta name="keywords" content="AI, content, writing, assistant">

    <title>ContentAI - AI Writing Assistant</title>
    <link rel="stylesheet" href="styles.css">
    <script src="app.js"></script>
  `,
  // Expected: needsEnhancedScraping = true (only meta tags, no substantial content)

  // Scenario 4: Mixed Content with Partial Loading (CONDITIONAL)
  mixedContentPartialLoading: `
    <meta name="description" content="AI tool for content generation">

    <h1>ContentAI</h1>
    <p>AI Writing Assistant</p>
    <div class="loading">Loading features...</div>
    <div class="loading">Loading pricing...</div>
    <div class="loading">Loading testimonials...</div>
  `
  // Expected: needsEnhancedScraping = true (minimal content, mostly loading indicators)
};
```

### 2. Image Collection Strategy (Favicon Priority)
```typescript
interface ImageCollectionStrategy {
  faviconPriority: true;     // Always prioritize favicons over logos
  screenshotType: 'viewport'; // Standard viewport (NOT fullpage)
  ogImageFallback: boolean;   // Use OG images as primary, screenshot as fallback
}

async function collectImagesWithPriority(url: string, content: string): Promise<ImageCollection> {
  const images: ImageCollection = {
    favicon: null,
    ogImages: [],
    screenshot: null
  };

  // Priority 1: Extract favicon (no additional cost)
  images.favicon = await extractFaviconPriority(url, content);

  // Priority 2: Extract OG images from meta tags (no additional cost)
  images.ogImages = extractOGImages(content);

  // Priority 3: Fallback to standard viewport screenshot if no OG images
  if (images.ogImages.length === 0) {
    images.screenshot = await captureViewportScreenshot(url);
  }

  return images;
}

async function extractFaviconPriority(url: string, content: string): Promise<string[]> {
  const faviconSelectors = [
    'link[rel="icon"]',                    // Standard favicon
    'link[rel="shortcut icon"]',           // Legacy favicon
    'link[rel="apple-touch-icon"]',        // Apple touch icon
    'link[rel="apple-touch-icon-precomposed"]' // Apple precomposed
  ];

  const faviconUrls = [];

  // Extract from HTML content (no additional API cost)
  faviconSelectors.forEach(selector => {
    const regex = new RegExp(`<${selector}[^>]*href=["']([^"']+)["']`, 'gi');
    let match;
    while ((match = regex.exec(content)) !== null) {
      faviconUrls.push(resolveUrl(match[1], url));
    }
  });

  // Fallback to default favicon.ico
  if (faviconUrls.length === 0) {
    const baseUrl = new URL(url).origin;
    faviconUrls.push(`${baseUrl}/favicon.ico`);
  }

  return [...new Set(faviconUrls)]; // Remove duplicates
}

async function captureViewportScreenshot(url: string): Promise<ScreenshotResult> {
  return await captureScreenshot(url, {
    screenShot: true,        // Standard viewport only (NOT fullScreenShot)
    width: 1200,            // Standard viewport width
    height: 800,            // Standard viewport height
    render: true,           // Required for screenshots
    blockResources: true,   // Optimize cost and performance
    timeout: 30000          // Reasonable timeout
  });
}
```

### 3. Cost-Controlled Re-scraping Logic
```typescript
class CostControlledReScraper {
  private maxRetries = 2;
  private creditThreshold = 50;

  async retryScrapeWithCostControl(url: string, initialResult: ScrapeResult): Promise<ScrapeResult> {
    // Check if re-scraping is needed and affordable
    if (!this.needsRetryScraping(initialResult)) {
      return initialResult;
    }

    const usage = await getUsageStatistics();
    if (usage.remainingMonthlyRequests < this.creditThreshold) {
      console.warn('Insufficient credits for enhanced scraping');
      return initialResult;
    }

    // Enhanced scraping with JavaScript rendering
    const enhancedResult = await scrapePage(url, {
      render: true,                    // Enable JavaScript (5x cost multiplier)
      waitUntil: 'networkidle2',      // Wait for network to be idle
      waitSelector: '.content, .main, article, [role="main"]', // Wait for content
      customWait: 3000,               // Additional 3-second wait
      blockResources: true,           // Block CSS/images for cost control
      timeout: 45000,                 // Extended timeout for JS rendering
      super: false                    // Keep using datacenter proxy initially
    });

    // If still insufficient, try with residential proxy (last resort)
    if (this.needsRetryScraping(enhancedResult) && usage.remainingMonthlyRequests >= 50) {
      return await scrapePage(url, {
        render: true,
        super: true,                  // Residential proxy (10x base cost)
        waitUntil: 'networkidle2',
        customWait: 5000,             // Longer wait for difficult sites
        blockResources: true
      });
    }

    return enhancedResult;
  }

  private needsRetryScraping(result: ScrapeResult): boolean {
    if (!result.success) return true;

    const content = result.content;
    return (
      content.length < 500 ||                    // Too short
      content.includes('Loading...') ||          // Still loading
      content.includes('Please wait') ||         // Loading indicator
      !/<h[1-6]/.test(content) ||               // No headings
      content.split('\n').length < 10           // Too few lines
    );
  }
}
```

## Advanced Scrape.do Features

### 1. Network Request Monitoring
```typescript
interface NetworkMonitoringOptions {
  returnJSON?: boolean; // Return network requests as JSON
  showFrames?: boolean; // Include iframe content
  showWebsocketRequests?: boolean; // Monitor websocket connections
  transparentResponse?: boolean; // Return raw target response
}

async function scrapeWithNetworkMonitoring(url: string, options: NetworkMonitoringOptions): Promise<NetworkScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    render: 'true' // Required for network monitoring
  });

  if (options.returnJSON) params.set('returnJSON', 'true');
  if (options.showFrames) params.set('showFrames', 'true');
  if (options.showWebsocketRequests) params.set('showWebsocketRequests', 'true');
  if (options.transparentResponse) params.set('transparentResponse', 'true');

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

  if (options.returnJSON) {
    const jsonData = await response.json();
    return {
      content: jsonData.content,
      networkRequests: jsonData.requests,
      frames: jsonData.frames,
      websockets: jsonData.websockets
    };
  }

  return {
    content: await response.text(),
    headers: Object.fromEntries(response.headers.entries())
  };
}
```

### 2. Cookie Management
```typescript
interface CookieOptions {
  setCookies?: string; // Set cookies for the request
  pureCookies?: boolean; // Return original Set-Cookie headers
}

async function scrapeWithCookies(url: string, cookieOptions: CookieOptions): Promise<ScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url
  });

  if (cookieOptions.setCookies) {
    params.set('setCookies', cookieOptions.setCookies);
  }

  if (cookieOptions.pureCookies) {
    params.set('pureCookies', 'true');
  }

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

  return {
    success: true,
    content: await response.text(),
    cookies: response.headers.get('scrape.do-cookies') || response.headers.get('set-cookie'),
    timestamp: new Date().toISOString()
  };
}

// Example: Set authentication cookies
const result = await scrapeWithCookies('https://example.com/dashboard', {
  setCookies: 'session_id=abc123; auth_token=xyz789',
  pureCookies: true
});
```

### 3. Webhook Integration for Async Processing
```typescript
interface WebhookOptions {
  callbackUrl: string; // Your webhook endpoint
  async: boolean; // Process asynchronously
}

async function scrapeWithWebhook(url: string, webhookOptions: WebhookOptions): Promise<WebhookResponse> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    callback: webhookOptions.callbackUrl
  });

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

  if (webhookOptions.async) {
    return {
      success: true,
      message: 'Request queued for processing',
      webhookUrl: webhookOptions.callbackUrl,
      timestamp: new Date().toISOString()
    };
  }

  return await response.json();
}

// Webhook handler for receiving results
export async function handleScrapeWebhook(req: Request): Promise<Response> {
  const webhookData = await req.json();

  // Process the scraped data
  await processScrapeResult({
    url: webhookData.url,
    content: webhookData.content,
    success: webhookData.success,
    timestamp: webhookData.timestamp
  });

  return new Response('OK', { status: 200 });
}
```

### 4. Usage Statistics and Monitoring
```typescript
async function getUsageStatistics(): Promise<UsageStats> {
  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/info/?token=${SCRAPE_DO_CONFIG.apiKey}`);

  if (!response.ok) {
    throw new Error(`Failed to fetch usage stats: ${response.status}`);
  }

  const stats = await response.json();

  return {
    isActive: stats.IsActive,
    concurrentRequests: stats.ConcurrentRequest,
    maxMonthlyRequests: stats.MaxMonthlyRequest,
    remainingConcurrentRequests: stats.RemainingConcurrentRequest,
    remainingMonthlyRequests: stats.RemainingMonthlyRequest,
    lastUpdated: new Date().toISOString()
  };
}

// Monitor usage and alert when limits are approached
async function monitorUsage(): Promise<void> {
  const stats = await getUsageStatistics();

  const monthlyUsagePercent = ((stats.maxMonthlyRequests - stats.remainingMonthlyRequests) / stats.maxMonthlyRequests) * 100;
  const concurrentUsagePercent = ((stats.concurrentRequests - stats.remainingConcurrentRequests) / stats.concurrentRequests) * 100;

  if (monthlyUsagePercent > 80) {
    console.warn(`Monthly usage at ${monthlyUsagePercent.toFixed(1)}% - consider upgrading plan`);
  }

  if (concurrentUsagePercent > 90) {
    console.warn(`Concurrent requests at ${concurrentUsagePercent.toFixed(1)}% - reduce parallel processing`);
  }
}
```

### 4. AI Content Generation Pipeline Integration
```typescript
interface ContentGenerationPipeline {
  scrapeOptimization: 'markdown-llm-ready';
  modelSelection: 'intelligent-routing';
  contextManagement: 'token-aware-splitting';
  outputFormat: 'structured-json';
}

class ScrapeToAIPipeline {
  async processScrapedContent(scrapedData: ScrapeResult): Promise<AIGeneratedContent> {
    // Step 1: Optimize scraped content for LLM consumption
    const optimizedContent = this.optimizeForLLM(scrapedData.content);

    // Step 2: Validate content quality before expensive AI processing
    const validation = this.validateContentQuality(optimizedContent, scrapedData.url);
    if (!validation.isValid) {
      throw new Error(`Content quality insufficient: ${validation.issues.join(', ')}`);
    }

    // Step 3: Select optimal AI model based on content characteristics
    const modelConfig = this.selectOptimalModel({
      contentSize: this.calculateTokenCount(optimizedContent),
      complexity: this.assessComplexity(optimizedContent),
      priority: 'quality'
    });

    // Step 4: Generate structured content with cost tracking
    const aiResult = await this.generateWithCostTracking(optimizedContent, modelConfig);

    return aiResult;
  }

  private optimizeForLLM(content: string): string {
    // Remove excessive whitespace and navigation elements
    content = content.replace(/\n{3,}/g, '\n\n');
    content = content.replace(/^(Navigation|Menu|Footer|Header)[\s\S]*?(?=\n#|\n\n|$)/gm, '');

    // Clean HTML artifacts and normalize headers
    content = content.replace(/<[^>]*>/g, '');
    content = content.replace(/^#{4,}/gm, '###');

    // Ensure content fits within token limits (reserve 20% for response)
    const maxLength = 50000; // ~12-15K tokens for Gemini input
    if (content.length > maxLength) {
      content = content.substring(0, maxLength) + '\n\n[Content truncated for AI processing]';
    }

    return content.trim();
  }

  private selectOptimalModel(criteria: ModelSelectionCriteria): ModelConfig {
    const { contentSize, complexity, priority } = criteria;

    // Use Gemini 2.5 Pro Preview for large content or complex analysis
    if (contentSize > 100000 || complexity === 'complex') {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: 1048576,
        reasoning: 'Large context window with advanced reasoning capabilities'
      };
    }

    // Use GPT-4o for speed-critical or medium-sized content
    if (priority === 'speed' || contentSize < 50000) {
      return {
        provider: 'openai',
        model: 'gpt-4o',
        maxTokens: 128000,
        reasoning: 'Optimized for speed with sufficient context'
      };
    }

    // Default to Gemini for cost optimization with implicit caching
    return {
      provider: 'openrouter',
      model: 'google/gemini-2.5-pro-preview',
      maxTokens: 1048576,
      reasoning: 'Cost-effective with implicit caching and large context'
    };
  }
}
```

### 5. Quality Assurance and Content Validation
```typescript
interface ContentValidation {
  minimumLength: number;
  structureRequirements: string[];
  errorDetection: string[];
  completenessChecks: string[];
}

class ContentQualityAssurance {
  private validationRules: ContentValidation = {
    minimumLength: 200,
    structureRequirements: ['headings', 'paragraphs', 'meaningful-text'],
    errorDetection: ['404', 'access denied', 'forbidden', 'not found'],
    completenessChecks: ['title', 'description', 'main-content']
  };

  validateContentQuality(content: string, url: string): ValidationResult {
    const issues: string[] = [];

    // Length validation
    if (content.length < this.validationRules.minimumLength) {
      issues.push(`Content too short (${content.length} chars, minimum ${this.validationRules.minimumLength})`);
    }

    // Structure validation
    if (!this.hasValidStructure(content)) {
      issues.push('Content lacks proper structure (headings, paragraphs)');
    }

    // Error page detection
    if (this.containsErrorIndicators(content)) {
      issues.push('Content appears to be an error page');
    }

    // Meaningful content check
    if (!this.hasMeaningfulContent(content)) {
      issues.push('Content lacks meaningful text or appears to be placeholder');
    }

    // AI readiness check
    if (!this.isAIReady(content)) {
      issues.push('Content not properly formatted for AI processing');
    }

    return {
      isValid: issues.length === 0,
      issues,
      contentLength: content.length,
      url,
      qualityScore: this.calculateQualityScore(content)
    };
  }

  private hasValidStructure(content: string): boolean {
    const hasHeadings = /<h[1-6]|^#{1,6}\s/.test(content);
    const hasParagraphs = content.split('\n\n').length > 3;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(content);

    return hasHeadings && (hasParagraphs || hasLists);
  }

  private containsErrorIndicators(content: string): boolean {
    const errorIndicators = this.validationRules.errorDetection;
    const lowerContent = content.toLowerCase();

    return errorIndicators.some(indicator => lowerContent.includes(indicator));
  }

  private hasMeaningfulContent(content: string): boolean {
    // Check for meaningful text patterns
    const meaningfulTextRegex = /[a-zA-Z]{10,}/;
    const wordCount = content.split(/\s+/).filter(word => word.length > 3).length;

    return meaningfulTextRegex.test(content) && wordCount > 50;
  }

  private isAIReady(content: string): boolean {
    // Check if content is properly formatted for AI consumption
    const hasStructuredData = /^#{1,3}\s/.test(content); // Has markdown headers
    const hasCleanText = !/<script|<style|<nav|<footer/i.test(content); // No unwanted HTML
    const hasReasonableLength = content.length > 100 && content.length < 100000;

    return hasStructuredData && hasCleanText && hasReasonableLength;
  }

  private calculateQualityScore(content: string): number {
    let score = 0;

    // Length score (0-30 points)
    const lengthScore = Math.min(30, (content.length / 2000) * 30);
    score += lengthScore;

    // Structure score (0-25 points)
    const headingCount = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length;
    const structureScore = Math.min(25, headingCount * 5);
    score += structureScore;

    // Content diversity score (0-25 points)
    const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;
    const diversityScore = Math.min(25, (uniqueWords / 100) * 25);
    score += diversityScore;

    // Readability score (0-20 points)
    const sentences = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = content.split(/\s+/).length / sentences;
    const readabilityScore = avgWordsPerSentence > 10 && avgWordsPerSentence < 25 ? 20 : 10;
    score += readabilityScore;

    return Math.round(score);
  }
}
```

## Configurable Multi-Page Scraping System

### 1. Page Detection and Scraping Configuration
```typescript
interface MultiPageScrapingConfig {
  enabled: boolean;
  mode: 'immediate' | 'queue_for_later' | 'conditional';
  maxPagesPerTool: number;
  creditThreshold: number; // Minimum credits before multi-page scraping

  pageTypes: {
    pricing: {
      enabled: boolean;
      priority: 'high' | 'medium' | 'low';
      patterns: string[]; // URL patterns to detect pricing pages
      selectors: string[]; // CSS selectors for pricing content
      required: boolean; // Whether to fail if not found
    };
    faq: {
      enabled: boolean;
      priority: 'high' | 'medium' | 'low';
      patterns: string[];
      selectors: string[];
      required: boolean;
    };
    features: {
      enabled: boolean;
      priority: 'high' | 'medium' | 'low';
      patterns: string[];
      selectors: string[];
      required: boolean;
    };
    about: {
      enabled: boolean;
      priority: 'high' | 'medium' | 'low';
      patterns: string[];
      selectors: string[];
      required: boolean;
    };
  };

  fallbackStrategy: {
    searchInMainPage: boolean; // Look for content in main page first
    useNavigation: boolean; // Follow navigation links
    useSitemap: boolean; // Check sitemap.xml
  };
}

const defaultMultiPageConfig: MultiPageScrapingConfig = {
  enabled: true,
  mode: 'conditional', // Smart decision based on content and credits
  maxPagesPerTool: 4,
  creditThreshold: 100,

  pageTypes: {
    pricing: {
      enabled: true,
      priority: 'high',
      patterns: [
        '/pricing', '/price', '/plans', '/subscription',
        '/cost', '/buy', '/purchase', '/upgrade'
      ],
      selectors: [
        '.pricing', '.plans', '.subscription',
        '[class*="price"]', '[id*="pricing"]'
      ],
      required: true
    },
    faq: {
      enabled: true,
      priority: 'medium',
      patterns: [
        '/faq', '/help', '/support', '/questions',
        '/q-and-a', '/frequently-asked'
      ],
      selectors: [
        '.faq', '.help', '.support',
        '[class*="faq"]', '[id*="faq"]'
      ],
      required: false
    },
    features: {
      enabled: true,
      priority: 'high',
      patterns: [
        '/features', '/capabilities', '/functionality',
        '/what-we-do', '/services'
      ],
      selectors: [
        '.features', '.capabilities',
        '[class*="feature"]', '[id*="features"]'
      ],
      required: true
    },
    about: {
      enabled: true,
      priority: 'low',
      patterns: [
        '/about', '/about-us', '/company',
        '/story', '/mission', '/team'
      ],
      selectors: [
        '.about', '.company', '.story',
        '[class*="about"]', '[id*="about"]'
      ],
      required: false
    }
  },

  fallbackStrategy: {
    searchInMainPage: true,
    useNavigation: true,
    useSitemap: false
  }
};
```

### 2. Intelligent Page Discovery and Queuing
```typescript
interface PageDiscoveryResult {
  pageType: 'pricing' | 'faq' | 'features' | 'about';
  url: string;
  confidence: number; // 0-100 confidence score
  foundMethod: 'navigation' | 'pattern' | 'content' | 'sitemap';
  priority: 'high' | 'medium' | 'low';
  estimatedCredits: number;
}

interface ScrapingDecision {
  scrapeNow: PageDiscoveryResult[];
  queueForLater: PageDiscoveryResult[];
  skipPages: PageDiscoveryResult[];
  reason: string;
}

class ConfigurableMultiPageScraper {
  constructor(private config: MultiPageScrapingConfig) {}

  async discoverAndPlanScraping(mainUrl: string, mainContent: string): Promise<ScrapingDecision> {
    const discoveredPages = await this.discoverPages(mainUrl, mainContent);
    const currentCredits = await this.getCurrentCredits();

    return this.makeScrapingDecision(discoveredPages, currentCredits);
  }

  private async discoverPages(mainUrl: string, mainContent: string): Promise<PageDiscoveryResult[]> {
    const discovered: PageDiscoveryResult[] = [];
    const baseUrl = new URL(mainUrl).origin;

    for (const [pageType, pageConfig] of Object.entries(this.config.pageTypes)) {
      if (!pageConfig.enabled) continue;

      // Method 1: Check if content exists in main page
      if (this.config.fallbackStrategy.searchInMainPage) {
        const contentFound = this.findContentInMainPage(mainContent, pageConfig.selectors);
        if (contentFound.found) {
          discovered.push({
            pageType: pageType as any,
            url: mainUrl,
            confidence: contentFound.confidence,
            foundMethod: 'content',
            priority: pageConfig.priority,
            estimatedCredits: 0 // No additional scraping needed
          });
          continue; // Found in main page, no need to look for separate page
        }
      }

      // Method 2: Look for navigation links
      if (this.config.fallbackStrategy.useNavigation) {
        const navLinks = this.findNavigationLinks(mainContent, pageConfig.patterns);
        for (const link of navLinks) {
          discovered.push({
            pageType: pageType as any,
            url: this.resolveUrl(link.url, baseUrl),
            confidence: link.confidence,
            foundMethod: 'navigation',
            priority: pageConfig.priority,
            estimatedCredits: this.estimateScrapingCost(link.url)
          });
        }
      }

      // Method 3: Pattern-based URL construction
      if (discovered.filter(p => p.pageType === pageType).length === 0) {
        const constructedUrls = this.constructUrls(baseUrl, pageConfig.patterns);
        for (const url of constructedUrls) {
          discovered.push({
            pageType: pageType as any,
            url,
            confidence: 30, // Lower confidence for constructed URLs
            foundMethod: 'pattern',
            priority: pageConfig.priority,
            estimatedCredits: this.estimateScrapingCost(url)
          });
        }
      }
    }

    return discovered.sort((a, b) => {
      // Sort by priority, then confidence
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return b.confidence - a.confidence;
    });
  }

  private makeScrapingDecision(pages: PageDiscoveryResult[], currentCredits: number): ScrapingDecision {
    const decision: ScrapingDecision = {
      scrapeNow: [],
      queueForLater: [],
      skipPages: [],
      reason: ''
    };

    let totalEstimatedCost = 0;

    // Prioritize high-priority and high-confidence pages
    for (const page of pages) {
      const pageCost = page.estimatedCredits;

      // Always scrape if content found in main page (no additional cost)
      if (page.estimatedCredits === 0) {
        decision.scrapeNow.push(page);
        continue;
      }

      // Check if we have enough credits for immediate scraping
      if (currentCredits >= this.config.creditThreshold + totalEstimatedCost + pageCost) {
        if (this.config.mode === 'immediate' ||
           (this.config.mode === 'conditional' && page.priority === 'high' && page.confidence > 70)) {
          decision.scrapeNow.push(page);
          totalEstimatedCost += pageCost;
        } else {
          decision.queueForLater.push(page);
        }
      } else {
        // Not enough credits - queue for later or skip
        if (this.config.pageTypes[page.pageType].required) {
          decision.queueForLater.push(page);
        } else {
          decision.skipPages.push(page);
        }
      }
    }

    decision.reason = `Credits: ${currentCredits}, Estimated cost: ${totalEstimatedCost}, Mode: ${this.config.mode}`;

    return decision;
  }

  private findContentInMainPage(content: string, selectors: string[]): { found: boolean; confidence: number } {
    let confidence = 0;
    let matches = 0;

    for (const selector of selectors) {
      // Convert CSS selector to regex pattern for content matching
      const pattern = selector.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
                            .replace(/\\\./g, '\\s*')
                            .replace(/\\\[/g, '[^>]*')
                            .replace(/\\\]/g, '[^>]*');

      const regex = new RegExp(pattern, 'gi');
      if (regex.test(content)) {
        matches++;
        confidence += 20;
      }
    }

    return {
      found: matches > 0,
      confidence: Math.min(100, confidence)
    };
  }

  private findNavigationLinks(content: string, patterns: string[]): Array<{ url: string; confidence: number }> {
    const links: Array<{ url: string; confidence: number }> = [];

    // Extract all links from content
    const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
    let match;

    while ((match = linkRegex.exec(content)) !== null) {
      const url = match[1];
      const linkText = match[2].toLowerCase();

      for (const pattern of patterns) {
        const patternLower = pattern.toLowerCase().replace('/', '');

        // Check URL pattern match
        if (url.toLowerCase().includes(pattern)) {
          links.push({ url, confidence: 90 });
          break;
        }

        // Check link text match
        if (linkText.includes(patternLower)) {
          links.push({ url, confidence: 80 });
          break;
        }
      }
    }

    return links;
  }

  private constructUrls(baseUrl: string, patterns: string[]): string[] {
    return patterns.map(pattern => `${baseUrl}${pattern}`);
  }

  private estimateScrapingCost(url: string): number {
    // Base cost estimation - can be refined based on historical data
    return 5; // Assume 5 credits per additional page (datacenter + browser)
  }

  private async getCurrentCredits(): Promise<number> {
    const usage = await getUsageStatistics();
    return usage.remainingMonthlyRequests;
  }

  private resolveUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http')) return url;
    if (url.startsWith('/')) return baseUrl + url;
    return baseUrl + '/' + url;
  }
}
```

## Multi-Page Scraping

### 1. Discover Related Pages
```typescript
async function discoverRelatedPages(url: string): Promise<RelatedPages> {
  const scrapeResult = await scrapePage(url);
  
  if (!scrapeResult.success) {
    return { pricing: [], faq: [], features: [], about: [] };
  }

  const content = scrapeResult.content;
  const baseUrl = new URL(url).origin;
  
  // Extract links that might contain relevant information
  const linkRegex = /<a\s+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
  const links = [];
  let match;

  while ((match = linkRegex.exec(content)) !== null) {
    const href = resolveUrl(match[1], url);
    const text = match[2].toLowerCase().trim();
    
    links.push({ url: href, text, type: categorizeLink(text) });
  }

  return {
    pricing: links.filter(link => link.type === 'pricing').map(link => link.url),
    faq: links.filter(link => link.type === 'faq').map(link => link.url),
    features: links.filter(link => link.type === 'features').map(link => link.url),
    about: links.filter(link => link.type === 'about').map(link => link.url)
  };
}

function categorizeLink(text: string): string {
  const categories = {
    pricing: ['pricing', 'price', 'cost', 'plan', 'subscription', 'billing'],
    faq: ['faq', 'help', 'support', 'question', 'answer'],
    features: ['feature', 'capability', 'function', 'tool', 'service'],
    about: ['about', 'company', 'team', 'story', 'mission']
  };

  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return category;
    }
  }

  return 'other';
}
```

### 2. Batch Scraping with Cost Optimization
```typescript
async function scrapeMultiplePages(
  urls: string[], 
  options: BatchScrapeOptions = {}
): Promise<BatchScrapeResult> {
  const results = [];
  const maxConcurrent = options.maxConcurrent || 3; // Limit concurrent requests
  const delay = options.delay || 2000; // Delay between requests
  
  // Process URLs in batches to manage costs
  for (let i = 0; i < urls.length; i += maxConcurrent) {
    const batch = urls.slice(i, i + maxConcurrent);
    
    const batchPromises = batch.map(async (url, index) => {
      // Add delay to avoid rate limiting
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      try {
        return await scrapePage(url, {
          output: 'markdown',
          render: true,
          timeout: 30000
        });
      } catch (error) {
        return {
          success: false,
          url,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Log progress
    console.log(`Completed batch ${Math.floor(i / maxConcurrent) + 1}/${Math.ceil(urls.length / maxConcurrent)}`);
  }

  return {
    totalUrls: urls.length,
    successCount: results.filter(r => r.success).length,
    failureCount: results.filter(r => !r.success).length,
    results
  };
}
```

## Error Handling & Recovery

### 1. Comprehensive Error Handling
```typescript
function handleScrapeError(error: any, url: string): ScrapeResult {
  const errorResult: ScrapeResult = {
    success: false,
    url,
    error: error.message,
    timestamp: new Date().toISOString(),
    retryable: false
  };

  // Categorize errors for appropriate handling
  if (error.response) {
    const status = error.response.status;
    
    switch (status) {
      case 429: // Rate limited
        errorResult.retryable = true;
        errorResult.retryAfter = parseInt(error.response.headers['retry-after']) || 60;
        break;
      case 500:
      case 502:
      case 503:
      case 504: // Server errors
        errorResult.retryable = true;
        break;
      case 403: // Forbidden
        errorResult.error = 'Access denied - check API key or target site restrictions';
        break;
      case 404: // Not found
        errorResult.error = 'Target URL not found';
        break;
      default:
        errorResult.error = `HTTP ${status}: ${error.response.statusText}`;
    }
  } else if (error.code === 'ECONNABORTED') {
    errorResult.error = 'Request timeout';
    errorResult.retryable = true;
  } else if (error.code === 'ENOTFOUND') {
    errorResult.error = 'DNS resolution failed';
  }

  return errorResult;
}
```

### 2. Retry Mechanism
```typescript
async function scrapeWithRetry(
  url: string, 
  options: ScrapeOptions = {}, 
  maxRetries: number = 3
): Promise<ScrapeResult> {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await scrapePage(url, options);
      
      if (result.success) {
        return result;
      }
      
      if (!result.retryable) {
        return result; // Don't retry non-retryable errors
      }
      
      lastError = result;
      
      // Exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
    } catch (error) {
      lastError = handleScrapeError(error, url);
      
      if (!lastError.retryable) {
        break;
      }
    }
  }
  
  return lastError || {
    success: false,
    url,
    error: 'Max retries exceeded',
    timestamp: new Date().toISOString()
  };
}
```

## Data Processing & Optimization

### 1. Markdown Optimization for LLM
```typescript
function optimizeMarkdownForLLM(content: string): string {
  // Remove excessive whitespace
  content = content.replace(/\n{3,}/g, '\n\n');
  
  // Remove navigation and footer content
  content = content.replace(/^(Navigation|Menu|Footer)[\s\S]*?(?=\n#|\n\n|$)/gm, '');
  
  // Clean up HTML artifacts
  content = content.replace(/<[^>]*>/g, '');
  
  // Normalize headers
  content = content.replace(/^#{4,}/gm, '###');
  
  // Remove empty sections
  content = content.replace(/^#+\s*$\n/gm, '');
  
  // Limit content length for token management
  const maxLength = 50000; // Approximately 12-15K tokens
  if (content.length > maxLength) {
    content = content.substring(0, maxLength) + '\n\n[Content truncated for processing]';
  }
  
  return content.trim();
}
```

### 2. Content Validation
```typescript
function validateScrapedContent(content: string, url: string): ValidationResult {
  const issues = [];
  
  // Check minimum content length
  if (content.length < 100) {
    issues.push('Content too short - may indicate scraping failure');
  }
  
  // Check for error indicators
  const errorIndicators = ['404', 'not found', 'access denied', 'forbidden'];
  if (errorIndicators.some(indicator => content.toLowerCase().includes(indicator))) {
    issues.push('Content contains error indicators');
  }
  
  // Check for meaningful content
  const meaningfulContentRegex = /[a-zA-Z]{10,}/;
  if (!meaningfulContentRegex.test(content)) {
    issues.push('Content lacks meaningful text');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    contentLength: content.length,
    url
  };
}
```

## Integration with Job System

### 1. Job Processing Integration
```typescript
export class ScrapeJobHandler {
  async processScrapeJob(job: ScrapeJob): Promise<JobResult> {
    const { url, options = {} } = job.data;
    
    try {
      // Update job status
      await updateJobStatus(job.id, 'processing', { step: 'scraping' });
      
      // Perform scraping
      const scrapeResult = await scrapeWithRetry(url, options);
      
      if (!scrapeResult.success) {
        throw new Error(scrapeResult.error);
      }
      
      // Extract media assets
      await updateJobStatus(job.id, 'processing', { step: 'extracting_media' });
      const ogImages = await extractOGImages(url);
      const favicon = await extractFavicon(url);
      
      // Optimize content for LLM
      await updateJobStatus(job.id, 'processing', { step: 'optimizing_content' });
      const optimizedContent = optimizeMarkdownForLLM(scrapeResult.content);
      
      // Validate content
      const validation = validateScrapedContent(optimizedContent, url);
      
      if (!validation.isValid) {
        console.warn(`Content validation issues for ${url}:`, validation.issues);
      }
      
      // Store results
      const result = {
        url,
        content: optimizedContent,
        ogImages,
        favicon,
        metadata: scrapeResult.metadata,
        validation,
        timestamp: new Date().toISOString()
      };
      
      await updateJobStatus(job.id, 'completed', { result });
      
      return {
        success: true,
        data: result
      };
      
    } catch (error) {
      await updateJobStatus(job.id, 'failed', { error: error.message });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}
```

## Real-World Testing Examples

### Claude.ai Case Study (Browser Rendering Required)

Based on the actual testing results you provided, here's how our system handles the claude.ai scenario:

```typescript
// Real-world test case: claude.ai
const claudeAITestCase = {
  url: 'https://claude.ai',

  // Without browser rendering - Returns partial data
  withoutBrowser: {
    apiCall: 'http://api.scrape.do/?token=xxx&url=https://claude.ai&output=markdown',
    result: 'partial/incomplete content - "half data" as observed',
    analysis: {
      hasMetaTags: true,
      hasLoadingIndicators: true,
      hasSubstantialContent: false,
      needsEnhancedScraping: true,
      scenario: 'meta_loading_no_content'
    }
  },

  // With browser rendering - Returns full content
  withBrowser: {
    apiCall: 'http://api.scrape.do/?token=xxx&url=https://claude.ai&output=markdown&render=true&waitUntil=networkidle0',
    result: 'full content successfully extracted',
    analysis: {
      hasMetaTags: true,
      hasLoadingIndicators: false,
      hasSubstantialContent: true,
      needsEnhancedScraping: false,
      scenario: 'content_sufficient'
    }
  }
};

// Automated handling of claude.ai scenario
async function handleClaudeAIScenario(): Promise<ScrapeResult> {
  const url = 'https://claude.ai';

  // Step 1: Initial scrape (cost-optimized)
  console.log('Attempting initial scrape without browser rendering...');
  const initialResult = await scrapePage(url, {
    super: false,
    render: false,
    output: 'markdown'
  });

  // Step 2: Analyze content quality
  const analysis = analyzeContentQuality(initialResult.content);
  console.log(`Analysis result: ${analysis.scenario}`);

  // Step 3: Automatic enhancement for claude.ai
  if (analysis.needsEnhancedScraping || checkKnownBrowserRequiredSites(url)) {
    console.log('Detected need for browser rendering - enhancing scrape...');

    const enhancedResult = await scrapePage(url, {
      super: false,                    // Keep datacenter proxy for cost control
      render: true,                    // Enable JavaScript execution
      output: 'markdown',              // AI-ready format
      waitUntil: 'networkidle0',      // Wait for complete network idle
      customWait: 3000,               // Additional wait for dynamic content
      blockResources: true,           // Block CSS/images for performance
      timeout: 45000                  // Extended timeout for JS rendering
    });

    console.log(`Enhanced scraping result: ${enhancedResult.success ? 'SUCCESS' : 'FAILED'}`);
    return enhancedResult;
  }

  return initialResult;
}
```

### Automated Testing Suite

```typescript
class RealWorldTestSuite {
  async runClaudeAITest(): Promise<TestResult> {
    const url = 'https://claude.ai';

    try {
      // Test 1: Verify initial scrape detects insufficient content
      const initialResult = await scrapePage(url, { render: false, output: 'markdown' });
      const analysis = analyzeContentQuality(initialResult.content);

      if (!analysis.needsEnhancedScraping) {
        return {
          testName: 'Claude.ai Initial Detection',
          passed: false,
          error: 'Failed to detect need for browser rendering',
          details: {
            contentLength: initialResult.content.length,
            scenario: analysis.scenario,
            confidence: analysis.confidence
          }
        };
      }

      // Test 2: Verify enhanced scrape gets full content
      const enhancedResult = await scrapePage(url, {
        render: true,
        waitUntil: 'networkidle0',
        output: 'markdown'
      });

      const enhancedAnalysis = analyzeContentQuality(enhancedResult.content);

      if (enhancedAnalysis.needsEnhancedScraping) {
        return {
          testName: 'Claude.ai Enhanced Scraping',
          passed: false,
          error: 'Enhanced scraping still insufficient',
          details: {
            initialLength: initialResult.content.length,
            enhancedLength: enhancedResult.content.length,
            improvementRatio: enhancedResult.content.length / initialResult.content.length
          }
        };
      }

      return {
        testName: 'Claude.ai Complete Test',
        passed: true,
        details: {
          initialContentLength: initialResult.content.length,
          enhancedContentLength: enhancedResult.content.length,
          improvementRatio: enhancedResult.content.length / initialResult.content.length,
          costIncrease: '5x (due to render=true)',
          recommendation: 'Auto-detect and enhance for claude.ai and similar sites'
        }
      };

    } catch (error) {
      return {
        testName: 'Claude.ai Test',
        passed: false,
        error: error.message
      };
    }
  }

  async validateKnownSitePatterns(): Promise<TestResult[]> {
    const knownBrowserRequiredSites = [
      'https://claude.ai',
      'https://chat.openai.com',
      'https://bard.google.com',
      'https://notion.so',
      'https://figma.com'
    ];

    const results = [];

    for (const url of knownBrowserRequiredSites) {
      const shouldRequireBrowser = checkKnownBrowserRequiredSites(url);

      results.push({
        testName: `Known Site Pattern: ${url}`,
        passed: shouldRequireBrowser,
        details: {
          url,
          detectedAsBrowserRequired: shouldRequireBrowser,
          expected: true
        }
      });
    }

    return results;
  }
}

// Usage example
const testSuite = new RealWorldTestSuite();

// Run claude.ai specific test
const claudeTest = await testSuite.runClaudeAITest();
console.log('Claude.ai Test Result:', claudeTest);

// Validate known site patterns
const patternTests = await testSuite.validateKnownSitePatterns();
console.log('Pattern Detection Tests:', patternTests);
```

### Configuration for Known Problematic Sites

```typescript
// Enhanced configuration for sites known to require browser rendering
const ENHANCED_SITE_CONFIG = {
  'claude.ai': {
    alwaysUseBrowser: true,
    waitCondition: 'networkidle0',
    customWait: 3000,
    timeout: 45000,
    reason: 'Heavy client-side rendering, requires full JS execution'
  },
  'chat.openai.com': {
    alwaysUseBrowser: true,
    waitCondition: 'networkidle2',
    customWait: 5000,
    timeout: 60000,
    reason: 'Authentication and dynamic content loading'
  },
  'notion.so': {
    alwaysUseBrowser: true,
    waitCondition: 'networkidle0',
    customWait: 2000,
    timeout: 40000,
    reason: 'Block-based content system requires JS'
  }
};

// Apply enhanced configuration automatically
function getEnhancedConfigForUrl(url: string): ScrapeOptions | null {
  const domain = new URL(url).hostname.replace('www.', '');

  for (const [configDomain, config] of Object.entries(ENHANCED_SITE_CONFIG)) {
    if (domain.includes(configDomain)) {
      return {
        render: true,
        waitUntil: config.waitCondition,
        customWait: config.customWait,
        timeout: config.timeout,
        blockResources: true, // Keep costs controlled
        output: 'markdown'
      };
    }
  }

  return null;
}
```

---

*This comprehensive integration guide now includes real-world testing examples based on actual scraping results, demonstrating how the intelligent content detection system automatically handles complex scenarios like claude.ai that require browser rendering for complete content extraction.*
