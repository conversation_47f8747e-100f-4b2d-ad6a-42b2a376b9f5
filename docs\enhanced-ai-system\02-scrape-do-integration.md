# Scrape.do API Integration Guide

## Overview

This document provides comprehensive integration specifications for the scrape.do API, which serves as the primary web scraping service for the enhanced AI-powered content generation system. The integration focuses on extracting structured data in markdown format optimized for LLM consumption.

## API Configuration

### Authentication
```typescript
const SCRAPE_DO_CONFIG = {
  apiKey: process.env.SCRAPE_DO_API_KEY, // 8e7e405ff81145c4afe447610ddb9a7f785f494dddc
  baseUrl: 'https://api.scrape.do',
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 2000 // 2 seconds
};
```

### Enhanced Request Structure
```typescript
interface ScrapeRequest {
  // Required Parameters
  token: string;
  url: string; // Must be URL encoded for API mode

  // Proxy Configuration
  super?: boolean; // Use Residential & Mobile proxy networks (10 credits)
  geoCode?: string; // Country targeting (us, uk, de, fr, etc.)
  regionalGeoCode?: string; // Continental targeting (na, eu, as, etc.)
  sessionId?: number; // Sticky sessions for consistent IP

  // Browser & Rendering
  render?: boolean; // Enable headless browser (5x cost multiplier)
  device?: 'desktop' | 'mobile' | 'tablet'; // Device type simulation
  width?: number; // Browser width (default: 1920)
  height?: number; // Browser height (default: 1080)
  waitUntil?: 'domcontentloaded' | 'load' | 'networkidle0' | 'networkidle2';
  customWait?: number; // Additional wait time in milliseconds
  waitSelector?: string; // CSS selector to wait for
  blockResources?: boolean; // Block CSS/images for performance (default: true)

  // Headers & Cookies
  customHeaders?: boolean; // Handle all request headers
  extraHeaders?: boolean; // Add/modify specific headers
  forwardHeaders?: boolean; // Forward your headers to target
  setCookies?: string; // Set cookies for the request

  // Output & Response
  output?: 'raw' | 'markdown'; // Output format
  screenShot?: boolean; // Capture screenshot
  fullScreenShot?: boolean; // Full page screenshot
  particularScreenShot?: string; // Screenshot specific element
  returnJSON?: boolean; // Return network requests as JSON
  showFrames?: boolean; // Include iframe content (requires render=true)
  showWebsocketRequests?: boolean; // Show websocket requests
  transparentResponse?: boolean; // Return raw target response
  pureCookies?: boolean; // Return original Set-Cookie headers

  // Request Control
  timeout?: number; // Request timeout (default: 60000ms)
  retryTimeout?: number; // Retry timeout (default: 15000ms)
  disableRetry?: boolean; // Disable retry mechanism
  disableRedirection?: boolean; // Disable following redirects
  callback?: string; // Webhook URL for async results

  // Browser Automation
  playWithBrowser?: Array<{
    Action: 'Click' | 'Wait' | 'Execute' | 'Scroll' | 'Type';
    Selector?: string;
    Timeout?: number;
    Execute?: string;
    Text?: string;
  }>;
}
```

## Core Integration Functions

### 1. Enhanced Page Scraping with Cost Optimization
```typescript
interface ScrapeOptions {
  // Proxy Configuration
  useResidentialProxy?: boolean; // Use super=true for residential/mobile
  geoTargeting?: string; // Country code for geo-targeting
  stickySession?: number; // Session ID for consistent IP

  // Browser Configuration
  enableJSRendering?: boolean; // Enable headless browser
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  waitCondition?: 'domcontentloaded' | 'load' | 'networkidle0' | 'networkidle2';
  customWaitTime?: number; // Additional wait in milliseconds
  waitForSelector?: string; // CSS selector to wait for

  // Output Configuration
  outputFormat?: 'raw' | 'markdown';
  captureScreenshot?: boolean;
  fullPageScreenshot?: boolean;
  includeNetworkRequests?: boolean;

  // Performance Optimization
  blockResources?: boolean; // Block CSS/images for faster scraping
  timeout?: number;
  retryTimeout?: number;
}

async function scrapePage(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
  // Build query parameters based on options
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url // URL will be encoded by URLSearchParams
  });

  // Add optional parameters
  if (options.useResidentialProxy) params.set('super', 'true');
  if (options.geoTargeting) params.set('geoCode', options.geoTargeting);
  if (options.stickySession) params.set('sessionId', options.stickySession.toString());
  if (options.enableJSRendering) params.set('render', 'true');
  if (options.deviceType) params.set('device', options.deviceType);
  if (options.waitCondition) params.set('waitUntil', options.waitCondition);
  if (options.customWaitTime) params.set('customWait', options.customWaitTime.toString());
  if (options.waitForSelector) params.set('waitSelector', options.waitForSelector);
  if (options.outputFormat) params.set('output', options.outputFormat);
  if (options.captureScreenshot) params.set('screenShot', 'true');
  if (options.fullPageScreenshot) params.set('fullScreenShot', 'true');
  if (options.includeNetworkRequests) params.set('returnJSON', 'true');
  if (options.blockResources !== undefined) params.set('blockResources', options.blockResources.toString());
  if (options.timeout) params.set('timeout', options.timeout.toString());
  if (options.retryTimeout) params.set('retryTimeout', options.retryTimeout.toString());

  const requestUrl = `${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`;

  try {
    const response = await fetch(requestUrl, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br' // Enable compression for better performance
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const content = await response.text();
    const metadata = extractMetadata(response.headers);

    return {
      success: true,
      content,
      metadata,
      timestamp: new Date().toISOString(),
      costInfo: {
        creditsUsed: calculateCreditsUsed(options),
        requestType: getRequestType(options)
      }
    };
  } catch (error) {
    return handleScrapeError(error, url, options);
  }
}

// Helper function to calculate credits used
function calculateCreditsUsed(options: ScrapeOptions): number {
  let credits = 1; // Base cost for datacenter proxy

  if (options.useResidentialProxy) {
    credits = 10; // Residential/Mobile proxy base cost
  }

  if (options.enableJSRendering) {
    credits *= 5; // 5x multiplier for headless browser
  }

  return credits;
}

// Helper function to determine request type
function getRequestType(options: ScrapeOptions): string {
  if (options.useResidentialProxy && options.enableJSRendering) {
    return 'Residential + Browser';
  } else if (options.useResidentialProxy) {
    return 'Residential Proxy';
  } else if (options.enableJSRendering) {
    return 'Datacenter + Browser';
  } else {
    return 'Datacenter Proxy';
  }
}
```

### 2. Open Graph Image Extraction
```typescript
async function extractOGImages(url: string): Promise<MediaAsset[]> {
  const scrapeResult = await scrapePage(url, {
    customHeaders: true,
    render: true
  });

  if (!scrapeResult.success) {
    throw new Error(`Failed to scrape ${url}: ${scrapeResult.error}`);
  }

  const ogImages = [];
  const content = scrapeResult.content;

  // Extract OG images from scraped content
  const ogImageRegex = /<meta\s+property=["']og:image["']\s+content=["']([^"']+)["']/gi;
  const twitterImageRegex = /<meta\s+name=["']twitter:image["']\s+content=["']([^"']+)["']/gi;
  const facebookImageRegex = /<meta\s+property=["']fb:image["']\s+content=["']([^"']+)["']/gi;

  let match;
  
  // Extract og:image
  while ((match = ogImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'og:image',
      url: resolveUrl(match[1], url),
      priority: 1
    });
  }

  // Extract twitter:image
  while ((match = twitterImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'twitter:image',
      url: resolveUrl(match[1], url),
      priority: 2
    });
  }

  // Extract facebook image
  while ((match = facebookImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'facebook:image',
      url: resolveUrl(match[1], url),
      priority: 3
    });
  }

  return ogImages.sort((a, b) => a.priority - b.priority);
}
```

### 3. Advanced Browser Automation
```typescript
interface BrowserAction {
  Action: 'Click' | 'Wait' | 'Execute' | 'Scroll' | 'Type';
  Selector?: string; // CSS selector for Click, Scroll, Type actions
  Timeout?: number; // Wait time in milliseconds for Wait action
  Execute?: string; // JavaScript code for Execute action
  Text?: string; // Text to type for Type action
}

async function automatedScraping(url: string, actions: BrowserAction[]): Promise<ScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    render: 'true', // Required for browser automation
    playWithBrowser: JSON.stringify(actions)
  });

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);
  return await response.text();
}

// Example: Automated form interaction
const loginAndScrape = await automatedScraping('https://example.com/login', [
  { Action: 'Click', Selector: '#login-button' },
  { Action: 'Wait', Timeout: 2000 },
  { Action: 'Type', Selector: '#username', Text: '<EMAIL>' },
  { Action: 'Type', Selector: '#password', Text: 'password123' },
  { Action: 'Click', Selector: '#submit' },
  { Action: 'Wait', Timeout: 3000 },
  { Action: 'Execute', Execute: 'document.querySelector("#dashboard").innerHTML' }
]);
```

### 4. Enhanced Screenshot Capture
```typescript
interface ScreenshotOptions {
  fullPage?: boolean; // Capture full page screenshot
  elementSelector?: string; // Capture specific element
  width?: number; // Browser width (default: 1920)
  height?: number; // Browser height (default: 1080)
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  waitForSelector?: string; // Wait for element before screenshot
  customWait?: number; // Additional wait time
}

async function captureScreenshot(url: string, options: ScreenshotOptions = {}): Promise<ScreenshotResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    render: 'true' // Required for screenshots
  });

  // Configure screenshot type
  if (options.fullPage) {
    params.set('fullScreenShot', 'true');
  } else if (options.elementSelector) {
    params.set('particularScreenShot', options.elementSelector);
  } else {
    params.set('screenShot', 'true');
  }

  // Configure browser settings
  if (options.width) params.set('width', options.width.toString());
  if (options.height) params.set('height', options.height.toString());
  if (options.deviceType) params.set('device', options.deviceType);
  if (options.waitForSelector) params.set('waitSelector', options.waitForSelector);
  if (options.customWait) params.set('customWait', options.customWait.toString());

  try {
    const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Screenshot capture failed: ${response.status}`);
    }

    const screenshotData = await response.arrayBuffer();

    return {
      success: true,
      screenshot: Buffer.from(screenshotData).toString('base64'),
      metadata: {
        width: options.width || 1920,
        height: options.height || 1080,
        fullPage: options.fullPage || false,
        capturedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}
```

### 5. Advanced Proxy Configuration
```typescript
interface ProxyConfiguration {
  useResidentialProxy?: boolean; // Use super=true for residential/mobile networks
  geoTargeting?: string; // Country code (us, uk, de, fr, ca, au, etc.)
  regionalTargeting?: string; // Regional targeting (na, eu, as, etc.)
  stickySession?: number; // Session ID for consistent IP across requests
  customHeaders?: boolean; // Handle all request headers
  forwardHeaders?: boolean; // Forward your headers to target
  extraHeaders?: Record<string, string>; // Additional headers to set
}

async function scrapeWithAdvancedProxy(url: string, proxyConfig: ProxyConfiguration): Promise<ScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url
  });

  // Configure proxy settings
  if (proxyConfig.useResidentialProxy) {
    params.set('super', 'true'); // 10 credits vs 1 for datacenter
  }

  if (proxyConfig.geoTargeting) {
    params.set('geoCode', proxyConfig.geoTargeting);
  }

  if (proxyConfig.regionalTargeting) {
    params.set('regionalGeoCode', proxyConfig.regionalTargeting);
  }

  if (proxyConfig.stickySession) {
    params.set('sessionId', proxyConfig.stickySession.toString());
  }

  if (proxyConfig.customHeaders) {
    params.set('customHeaders', 'true');
  }

  if (proxyConfig.forwardHeaders) {
    params.set('forwardHeaders', 'true');
  }

  if (proxyConfig.extraHeaders) {
    params.set('extraHeaders', 'true');
    // Note: Extra headers would be sent in request body for POST requests
  }

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);
  return await response.text();
}

// Example: Scrape with US residential proxy and sticky session
const result = await scrapeWithAdvancedProxy('https://example.com', {
  useResidentialProxy: true,
  geoTargeting: 'us',
  stickySession: 12345,
  customHeaders: true
});
```

### 6. Cost Optimization Strategies
```typescript
interface CostOptimizationConfig {
  preferDatacenter?: boolean; // Use datacenter proxy when possible (1 credit vs 10)
  blockResources?: boolean; // Block CSS/images for faster scraping
  useMarkdownOutput?: boolean; // Get structured output for AI processing
  batchRequests?: boolean; // Process multiple URLs efficiently
  enableRetry?: boolean; // Enable smart retry mechanism
}

class CostOptimizedScraper {
  private config: CostOptimizationConfig;

  constructor(config: CostOptimizationConfig = {}) {
    this.config = {
      preferDatacenter: true,
      blockResources: true,
      useMarkdownOutput: true,
      batchRequests: true,
      enableRetry: true,
      ...config
    };
  }

  async scrapeWithOptimization(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
    const params = new URLSearchParams({
      token: SCRAPE_DO_CONFIG.apiKey,
      url: url
    });

    // Apply cost optimizations
    if (this.config.blockResources) {
      params.set('blockResources', 'true');
    }

    if (this.config.useMarkdownOutput) {
      params.set('output', 'markdown');
    }

    // Only use residential proxy if specifically needed
    if (options.requiresResidentialProxy) {
      params.set('super', 'true');
    }

    // Enable browser only if JavaScript is required
    if (options.requiresJavaScript) {
      params.set('render', 'true');
    }

    if (!this.config.enableRetry) {
      params.set('disableRetry', 'true');
    }

    try {
      const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();
      const creditsUsed = this.calculateCreditsUsed(options);

      return {
        success: true,
        content,
        metadata: {
          creditsUsed,
          proxyType: options.requiresResidentialProxy ? 'residential' : 'datacenter',
          browserEnabled: options.requiresJavaScript || false
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  private calculateCreditsUsed(options: ScrapeOptions): number {
    let credits = 1; // Base datacenter cost

    if (options.requiresResidentialProxy) {
      credits = 10; // Residential proxy cost
    }

    if (options.requiresJavaScript) {
      credits *= 5; // Browser rendering multiplier
    }

    return credits;
  }
}
```

### 7. Favicon Extraction
```typescript
async function extractFavicon(url: string): Promise<FaviconResult> {
  const scrapeResult = await scrapePage(url, {
    customHeaders: true,
    render: true
  });

  if (!scrapeResult.success) {
    throw new Error(`Failed to scrape ${url}: ${scrapeResult.error}`);
  }

  const content = scrapeResult.content;
  const faviconUrls = [];

  // Extract favicon URLs from various sources
  const faviconRegexes = [
    /<link\s+rel=["']icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']shortcut icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']apple-touch-icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']apple-touch-icon-precomposed["']\s+href=["']([^"']+)["']/gi
  ];

  faviconRegexes.forEach(regex => {
    let match;
    while ((match = regex.exec(content)) !== null) {
      faviconUrls.push(resolveUrl(match[1], url));
    }
  });

  // Add default favicon.ico if no favicon found
  if (faviconUrls.length === 0) {
    const baseUrl = new URL(url).origin;
    faviconUrls.push(`${baseUrl}/favicon.ico`);
  }

  return {
    faviconUrls: [...new Set(faviconUrls)], // Remove duplicates
    primaryFavicon: faviconUrls[0] || null
  };
}
```

## Complete Scraping and Content Generation Workflow

### 1. Intelligent Scraping Strategy
```typescript
interface ScrapingStrategy {
  initialAttempt: 'datacenter-basic';    // 1 credit - try first
  fallbackProxy: 'residential';          // 10 credits - if blocked
  renderingTrigger: 'content-detection'; // 5x multiplier - if needed
  costThreshold: number;                  // Credit limit checks
}

class IntelligentScraper {
  async scrapeWithStrategy(url: string): Promise<ScrapeResult> {
    // Step 1: Cost-optimized initial attempt
    let result = await this.basicScrape(url, {
      super: false,        // Datacenter proxy (1 credit)
      render: false,       // No browser rendering
      output: 'markdown',  // AI-ready format
      blockResources: true // Optimize performance
    });

    // Step 2: Content validation and re-scraping decision
    if (this.detectClientSideContent(result.content)) {
      result = await this.enhancedScrape(url, {
        render: true,                    // Enable JavaScript (5x cost)
        waitUntil: 'networkidle2',      // Wait for content to load
        waitSelector: '.main-content, article, [role="main"]',
        customWait: 3000,               // Additional wait time
        blockResources: true            // Keep costs controlled
      });
    }

    return result;
  }

  private detectClientSideContent(content: string): boolean {
    const indicators = [
      'Loading...', 'Please wait', 'loading-spinner',
      '<div id="root"></div>', '<div id="app"></div>',
      content.length < 500,              // Minimal content
      !/<h[1-6]/.test(content),         // No headings found
      /<script.*react.*<\/script>/i.test(content) // React indicators
    ];

    return indicators.some(indicator =>
      typeof indicator === 'string' ? content.includes(indicator) : indicator
    );
  }
}
```

### 2. Image Collection Strategy (Favicon Priority)
```typescript
interface ImageCollectionStrategy {
  faviconPriority: true;     // Always prioritize favicons over logos
  screenshotType: 'viewport'; // Standard viewport (NOT fullpage)
  ogImageFallback: boolean;   // Use OG images as primary, screenshot as fallback
}

async function collectImagesWithPriority(url: string, content: string): Promise<ImageCollection> {
  const images: ImageCollection = {
    favicon: null,
    ogImages: [],
    screenshot: null
  };

  // Priority 1: Extract favicon (no additional cost)
  images.favicon = await extractFaviconPriority(url, content);

  // Priority 2: Extract OG images from meta tags (no additional cost)
  images.ogImages = extractOGImages(content);

  // Priority 3: Fallback to standard viewport screenshot if no OG images
  if (images.ogImages.length === 0) {
    images.screenshot = await captureViewportScreenshot(url);
  }

  return images;
}

async function extractFaviconPriority(url: string, content: string): Promise<string[]> {
  const faviconSelectors = [
    'link[rel="icon"]',                    // Standard favicon
    'link[rel="shortcut icon"]',           // Legacy favicon
    'link[rel="apple-touch-icon"]',        // Apple touch icon
    'link[rel="apple-touch-icon-precomposed"]' // Apple precomposed
  ];

  const faviconUrls = [];

  // Extract from HTML content (no additional API cost)
  faviconSelectors.forEach(selector => {
    const regex = new RegExp(`<${selector}[^>]*href=["']([^"']+)["']`, 'gi');
    let match;
    while ((match = regex.exec(content)) !== null) {
      faviconUrls.push(resolveUrl(match[1], url));
    }
  });

  // Fallback to default favicon.ico
  if (faviconUrls.length === 0) {
    const baseUrl = new URL(url).origin;
    faviconUrls.push(`${baseUrl}/favicon.ico`);
  }

  return [...new Set(faviconUrls)]; // Remove duplicates
}

async function captureViewportScreenshot(url: string): Promise<ScreenshotResult> {
  return await captureScreenshot(url, {
    screenShot: true,        // Standard viewport only (NOT fullScreenShot)
    width: 1200,            // Standard viewport width
    height: 800,            // Standard viewport height
    render: true,           // Required for screenshots
    blockResources: true,   // Optimize cost and performance
    timeout: 30000          // Reasonable timeout
  });
}
```

### 3. Cost-Controlled Re-scraping Logic
```typescript
class CostControlledReScraper {
  private maxRetries = 2;
  private creditThreshold = 50;

  async retryScrapeWithCostControl(url: string, initialResult: ScrapeResult): Promise<ScrapeResult> {
    // Check if re-scraping is needed and affordable
    if (!this.needsRetryScraping(initialResult)) {
      return initialResult;
    }

    const usage = await getUsageStatistics();
    if (usage.remainingMonthlyRequests < this.creditThreshold) {
      console.warn('Insufficient credits for enhanced scraping');
      return initialResult;
    }

    // Enhanced scraping with JavaScript rendering
    const enhancedResult = await scrapePage(url, {
      render: true,                    // Enable JavaScript (5x cost multiplier)
      waitUntil: 'networkidle2',      // Wait for network to be idle
      waitSelector: '.content, .main, article, [role="main"]', // Wait for content
      customWait: 3000,               // Additional 3-second wait
      blockResources: true,           // Block CSS/images for cost control
      timeout: 45000,                 // Extended timeout for JS rendering
      super: false                    // Keep using datacenter proxy initially
    });

    // If still insufficient, try with residential proxy (last resort)
    if (this.needsRetryScraping(enhancedResult) && usage.remainingMonthlyRequests >= 50) {
      return await scrapePage(url, {
        render: true,
        super: true,                  // Residential proxy (10x base cost)
        waitUntil: 'networkidle2',
        customWait: 5000,             // Longer wait for difficult sites
        blockResources: true
      });
    }

    return enhancedResult;
  }

  private needsRetryScraping(result: ScrapeResult): boolean {
    if (!result.success) return true;

    const content = result.content;
    return (
      content.length < 500 ||                    // Too short
      content.includes('Loading...') ||          // Still loading
      content.includes('Please wait') ||         // Loading indicator
      !/<h[1-6]/.test(content) ||               // No headings
      content.split('\n').length < 10           // Too few lines
    );
  }
}
```

## Advanced Scrape.do Features

### 1. Network Request Monitoring
```typescript
interface NetworkMonitoringOptions {
  returnJSON?: boolean; // Return network requests as JSON
  showFrames?: boolean; // Include iframe content
  showWebsocketRequests?: boolean; // Monitor websocket connections
  transparentResponse?: boolean; // Return raw target response
}

async function scrapeWithNetworkMonitoring(url: string, options: NetworkMonitoringOptions): Promise<NetworkScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    render: 'true' // Required for network monitoring
  });

  if (options.returnJSON) params.set('returnJSON', 'true');
  if (options.showFrames) params.set('showFrames', 'true');
  if (options.showWebsocketRequests) params.set('showWebsocketRequests', 'true');
  if (options.transparentResponse) params.set('transparentResponse', 'true');

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

  if (options.returnJSON) {
    const jsonData = await response.json();
    return {
      content: jsonData.content,
      networkRequests: jsonData.requests,
      frames: jsonData.frames,
      websockets: jsonData.websockets
    };
  }

  return {
    content: await response.text(),
    headers: Object.fromEntries(response.headers.entries())
  };
}
```

### 2. Cookie Management
```typescript
interface CookieOptions {
  setCookies?: string; // Set cookies for the request
  pureCookies?: boolean; // Return original Set-Cookie headers
}

async function scrapeWithCookies(url: string, cookieOptions: CookieOptions): Promise<ScrapeResult> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url
  });

  if (cookieOptions.setCookies) {
    params.set('setCookies', cookieOptions.setCookies);
  }

  if (cookieOptions.pureCookies) {
    params.set('pureCookies', 'true');
  }

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

  return {
    success: true,
    content: await response.text(),
    cookies: response.headers.get('scrape.do-cookies') || response.headers.get('set-cookie'),
    timestamp: new Date().toISOString()
  };
}

// Example: Set authentication cookies
const result = await scrapeWithCookies('https://example.com/dashboard', {
  setCookies: 'session_id=abc123; auth_token=xyz789',
  pureCookies: true
});
```

### 3. Webhook Integration for Async Processing
```typescript
interface WebhookOptions {
  callbackUrl: string; // Your webhook endpoint
  async: boolean; // Process asynchronously
}

async function scrapeWithWebhook(url: string, webhookOptions: WebhookOptions): Promise<WebhookResponse> {
  const params = new URLSearchParams({
    token: SCRAPE_DO_CONFIG.apiKey,
    url: url,
    callback: webhookOptions.callbackUrl
  });

  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/?${params.toString()}`);

  if (webhookOptions.async) {
    return {
      success: true,
      message: 'Request queued for processing',
      webhookUrl: webhookOptions.callbackUrl,
      timestamp: new Date().toISOString()
    };
  }

  return await response.json();
}

// Webhook handler for receiving results
export async function handleScrapeWebhook(req: Request): Promise<Response> {
  const webhookData = await req.json();

  // Process the scraped data
  await processScrapeResult({
    url: webhookData.url,
    content: webhookData.content,
    success: webhookData.success,
    timestamp: webhookData.timestamp
  });

  return new Response('OK', { status: 200 });
}
```

### 4. Usage Statistics and Monitoring
```typescript
async function getUsageStatistics(): Promise<UsageStats> {
  const response = await fetch(`${SCRAPE_DO_CONFIG.baseUrl}/info/?token=${SCRAPE_DO_CONFIG.apiKey}`);

  if (!response.ok) {
    throw new Error(`Failed to fetch usage stats: ${response.status}`);
  }

  const stats = await response.json();

  return {
    isActive: stats.IsActive,
    concurrentRequests: stats.ConcurrentRequest,
    maxMonthlyRequests: stats.MaxMonthlyRequest,
    remainingConcurrentRequests: stats.RemainingConcurrentRequest,
    remainingMonthlyRequests: stats.RemainingMonthlyRequest,
    lastUpdated: new Date().toISOString()
  };
}

// Monitor usage and alert when limits are approached
async function monitorUsage(): Promise<void> {
  const stats = await getUsageStatistics();

  const monthlyUsagePercent = ((stats.maxMonthlyRequests - stats.remainingMonthlyRequests) / stats.maxMonthlyRequests) * 100;
  const concurrentUsagePercent = ((stats.concurrentRequests - stats.remainingConcurrentRequests) / stats.concurrentRequests) * 100;

  if (monthlyUsagePercent > 80) {
    console.warn(`Monthly usage at ${monthlyUsagePercent.toFixed(1)}% - consider upgrading plan`);
  }

  if (concurrentUsagePercent > 90) {
    console.warn(`Concurrent requests at ${concurrentUsagePercent.toFixed(1)}% - reduce parallel processing`);
  }
}
```

### 4. AI Content Generation Pipeline Integration
```typescript
interface ContentGenerationPipeline {
  scrapeOptimization: 'markdown-llm-ready';
  modelSelection: 'intelligent-routing';
  contextManagement: 'token-aware-splitting';
  outputFormat: 'structured-json';
}

class ScrapeToAIPipeline {
  async processScrapedContent(scrapedData: ScrapeResult): Promise<AIGeneratedContent> {
    // Step 1: Optimize scraped content for LLM consumption
    const optimizedContent = this.optimizeForLLM(scrapedData.content);

    // Step 2: Validate content quality before expensive AI processing
    const validation = this.validateContentQuality(optimizedContent, scrapedData.url);
    if (!validation.isValid) {
      throw new Error(`Content quality insufficient: ${validation.issues.join(', ')}`);
    }

    // Step 3: Select optimal AI model based on content characteristics
    const modelConfig = this.selectOptimalModel({
      contentSize: this.calculateTokenCount(optimizedContent),
      complexity: this.assessComplexity(optimizedContent),
      priority: 'quality'
    });

    // Step 4: Generate structured content with cost tracking
    const aiResult = await this.generateWithCostTracking(optimizedContent, modelConfig);

    return aiResult;
  }

  private optimizeForLLM(content: string): string {
    // Remove excessive whitespace and navigation elements
    content = content.replace(/\n{3,}/g, '\n\n');
    content = content.replace(/^(Navigation|Menu|Footer|Header)[\s\S]*?(?=\n#|\n\n|$)/gm, '');

    // Clean HTML artifacts and normalize headers
    content = content.replace(/<[^>]*>/g, '');
    content = content.replace(/^#{4,}/gm, '###');

    // Ensure content fits within token limits (reserve 20% for response)
    const maxLength = 50000; // ~12-15K tokens for Gemini input
    if (content.length > maxLength) {
      content = content.substring(0, maxLength) + '\n\n[Content truncated for AI processing]';
    }

    return content.trim();
  }

  private selectOptimalModel(criteria: ModelSelectionCriteria): ModelConfig {
    const { contentSize, complexity, priority } = criteria;

    // Use Gemini 2.5 Pro Preview for large content or complex analysis
    if (contentSize > 100000 || complexity === 'complex') {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: 1048576,
        reasoning: 'Large context window with advanced reasoning capabilities'
      };
    }

    // Use GPT-4o for speed-critical or medium-sized content
    if (priority === 'speed' || contentSize < 50000) {
      return {
        provider: 'openai',
        model: 'gpt-4o',
        maxTokens: 128000,
        reasoning: 'Optimized for speed with sufficient context'
      };
    }

    // Default to Gemini for cost optimization with implicit caching
    return {
      provider: 'openrouter',
      model: 'google/gemini-2.5-pro-preview',
      maxTokens: 1048576,
      reasoning: 'Cost-effective with implicit caching and large context'
    };
  }
}
```

### 5. Quality Assurance and Content Validation
```typescript
interface ContentValidation {
  minimumLength: number;
  structureRequirements: string[];
  errorDetection: string[];
  completenessChecks: string[];
}

class ContentQualityAssurance {
  private validationRules: ContentValidation = {
    minimumLength: 200,
    structureRequirements: ['headings', 'paragraphs', 'meaningful-text'],
    errorDetection: ['404', 'access denied', 'forbidden', 'not found'],
    completenessChecks: ['title', 'description', 'main-content']
  };

  validateContentQuality(content: string, url: string): ValidationResult {
    const issues: string[] = [];

    // Length validation
    if (content.length < this.validationRules.minimumLength) {
      issues.push(`Content too short (${content.length} chars, minimum ${this.validationRules.minimumLength})`);
    }

    // Structure validation
    if (!this.hasValidStructure(content)) {
      issues.push('Content lacks proper structure (headings, paragraphs)');
    }

    // Error page detection
    if (this.containsErrorIndicators(content)) {
      issues.push('Content appears to be an error page');
    }

    // Meaningful content check
    if (!this.hasMeaningfulContent(content)) {
      issues.push('Content lacks meaningful text or appears to be placeholder');
    }

    // AI readiness check
    if (!this.isAIReady(content)) {
      issues.push('Content not properly formatted for AI processing');
    }

    return {
      isValid: issues.length === 0,
      issues,
      contentLength: content.length,
      url,
      qualityScore: this.calculateQualityScore(content)
    };
  }

  private hasValidStructure(content: string): boolean {
    const hasHeadings = /<h[1-6]|^#{1,6}\s/.test(content);
    const hasParagraphs = content.split('\n\n').length > 3;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(content);

    return hasHeadings && (hasParagraphs || hasLists);
  }

  private containsErrorIndicators(content: string): boolean {
    const errorIndicators = this.validationRules.errorDetection;
    const lowerContent = content.toLowerCase();

    return errorIndicators.some(indicator => lowerContent.includes(indicator));
  }

  private hasMeaningfulContent(content: string): boolean {
    // Check for meaningful text patterns
    const meaningfulTextRegex = /[a-zA-Z]{10,}/;
    const wordCount = content.split(/\s+/).filter(word => word.length > 3).length;

    return meaningfulTextRegex.test(content) && wordCount > 50;
  }

  private isAIReady(content: string): boolean {
    // Check if content is properly formatted for AI consumption
    const hasStructuredData = /^#{1,3}\s/.test(content); // Has markdown headers
    const hasCleanText = !/<script|<style|<nav|<footer/i.test(content); // No unwanted HTML
    const hasReasonableLength = content.length > 100 && content.length < 100000;

    return hasStructuredData && hasCleanText && hasReasonableLength;
  }

  private calculateQualityScore(content: string): number {
    let score = 0;

    // Length score (0-30 points)
    const lengthScore = Math.min(30, (content.length / 2000) * 30);
    score += lengthScore;

    // Structure score (0-25 points)
    const headingCount = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length;
    const structureScore = Math.min(25, headingCount * 5);
    score += structureScore;

    // Content diversity score (0-25 points)
    const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;
    const diversityScore = Math.min(25, (uniqueWords / 100) * 25);
    score += diversityScore;

    // Readability score (0-20 points)
    const sentences = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = content.split(/\s+/).length / sentences;
    const readabilityScore = avgWordsPerSentence > 10 && avgWordsPerSentence < 25 ? 20 : 10;
    score += readabilityScore;

    return Math.round(score);
  }
}
```

## Multi-Page Scraping

### 1. Discover Related Pages
```typescript
async function discoverRelatedPages(url: string): Promise<RelatedPages> {
  const scrapeResult = await scrapePage(url);
  
  if (!scrapeResult.success) {
    return { pricing: [], faq: [], features: [], about: [] };
  }

  const content = scrapeResult.content;
  const baseUrl = new URL(url).origin;
  
  // Extract links that might contain relevant information
  const linkRegex = /<a\s+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
  const links = [];
  let match;

  while ((match = linkRegex.exec(content)) !== null) {
    const href = resolveUrl(match[1], url);
    const text = match[2].toLowerCase().trim();
    
    links.push({ url: href, text, type: categorizeLink(text) });
  }

  return {
    pricing: links.filter(link => link.type === 'pricing').map(link => link.url),
    faq: links.filter(link => link.type === 'faq').map(link => link.url),
    features: links.filter(link => link.type === 'features').map(link => link.url),
    about: links.filter(link => link.type === 'about').map(link => link.url)
  };
}

function categorizeLink(text: string): string {
  const categories = {
    pricing: ['pricing', 'price', 'cost', 'plan', 'subscription', 'billing'],
    faq: ['faq', 'help', 'support', 'question', 'answer'],
    features: ['feature', 'capability', 'function', 'tool', 'service'],
    about: ['about', 'company', 'team', 'story', 'mission']
  };

  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return category;
    }
  }

  return 'other';
}
```

### 2. Batch Scraping with Cost Optimization
```typescript
async function scrapeMultiplePages(
  urls: string[], 
  options: BatchScrapeOptions = {}
): Promise<BatchScrapeResult> {
  const results = [];
  const maxConcurrent = options.maxConcurrent || 3; // Limit concurrent requests
  const delay = options.delay || 2000; // Delay between requests
  
  // Process URLs in batches to manage costs
  for (let i = 0; i < urls.length; i += maxConcurrent) {
    const batch = urls.slice(i, i + maxConcurrent);
    
    const batchPromises = batch.map(async (url, index) => {
      // Add delay to avoid rate limiting
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      try {
        return await scrapePage(url, {
          output: 'markdown',
          render: true,
          timeout: 30000
        });
      } catch (error) {
        return {
          success: false,
          url,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Log progress
    console.log(`Completed batch ${Math.floor(i / maxConcurrent) + 1}/${Math.ceil(urls.length / maxConcurrent)}`);
  }

  return {
    totalUrls: urls.length,
    successCount: results.filter(r => r.success).length,
    failureCount: results.filter(r => !r.success).length,
    results
  };
}
```

## Error Handling & Recovery

### 1. Comprehensive Error Handling
```typescript
function handleScrapeError(error: any, url: string): ScrapeResult {
  const errorResult: ScrapeResult = {
    success: false,
    url,
    error: error.message,
    timestamp: new Date().toISOString(),
    retryable: false
  };

  // Categorize errors for appropriate handling
  if (error.response) {
    const status = error.response.status;
    
    switch (status) {
      case 429: // Rate limited
        errorResult.retryable = true;
        errorResult.retryAfter = parseInt(error.response.headers['retry-after']) || 60;
        break;
      case 500:
      case 502:
      case 503:
      case 504: // Server errors
        errorResult.retryable = true;
        break;
      case 403: // Forbidden
        errorResult.error = 'Access denied - check API key or target site restrictions';
        break;
      case 404: // Not found
        errorResult.error = 'Target URL not found';
        break;
      default:
        errorResult.error = `HTTP ${status}: ${error.response.statusText}`;
    }
  } else if (error.code === 'ECONNABORTED') {
    errorResult.error = 'Request timeout';
    errorResult.retryable = true;
  } else if (error.code === 'ENOTFOUND') {
    errorResult.error = 'DNS resolution failed';
  }

  return errorResult;
}
```

### 2. Retry Mechanism
```typescript
async function scrapeWithRetry(
  url: string, 
  options: ScrapeOptions = {}, 
  maxRetries: number = 3
): Promise<ScrapeResult> {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await scrapePage(url, options);
      
      if (result.success) {
        return result;
      }
      
      if (!result.retryable) {
        return result; // Don't retry non-retryable errors
      }
      
      lastError = result;
      
      // Exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
    } catch (error) {
      lastError = handleScrapeError(error, url);
      
      if (!lastError.retryable) {
        break;
      }
    }
  }
  
  return lastError || {
    success: false,
    url,
    error: 'Max retries exceeded',
    timestamp: new Date().toISOString()
  };
}
```

## Data Processing & Optimization

### 1. Markdown Optimization for LLM
```typescript
function optimizeMarkdownForLLM(content: string): string {
  // Remove excessive whitespace
  content = content.replace(/\n{3,}/g, '\n\n');
  
  // Remove navigation and footer content
  content = content.replace(/^(Navigation|Menu|Footer)[\s\S]*?(?=\n#|\n\n|$)/gm, '');
  
  // Clean up HTML artifacts
  content = content.replace(/<[^>]*>/g, '');
  
  // Normalize headers
  content = content.replace(/^#{4,}/gm, '###');
  
  // Remove empty sections
  content = content.replace(/^#+\s*$\n/gm, '');
  
  // Limit content length for token management
  const maxLength = 50000; // Approximately 12-15K tokens
  if (content.length > maxLength) {
    content = content.substring(0, maxLength) + '\n\n[Content truncated for processing]';
  }
  
  return content.trim();
}
```

### 2. Content Validation
```typescript
function validateScrapedContent(content: string, url: string): ValidationResult {
  const issues = [];
  
  // Check minimum content length
  if (content.length < 100) {
    issues.push('Content too short - may indicate scraping failure');
  }
  
  // Check for error indicators
  const errorIndicators = ['404', 'not found', 'access denied', 'forbidden'];
  if (errorIndicators.some(indicator => content.toLowerCase().includes(indicator))) {
    issues.push('Content contains error indicators');
  }
  
  // Check for meaningful content
  const meaningfulContentRegex = /[a-zA-Z]{10,}/;
  if (!meaningfulContentRegex.test(content)) {
    issues.push('Content lacks meaningful text');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    contentLength: content.length,
    url
  };
}
```

## Integration with Job System

### 1. Job Processing Integration
```typescript
export class ScrapeJobHandler {
  async processScrapeJob(job: ScrapeJob): Promise<JobResult> {
    const { url, options = {} } = job.data;
    
    try {
      // Update job status
      await updateJobStatus(job.id, 'processing', { step: 'scraping' });
      
      // Perform scraping
      const scrapeResult = await scrapeWithRetry(url, options);
      
      if (!scrapeResult.success) {
        throw new Error(scrapeResult.error);
      }
      
      // Extract media assets
      await updateJobStatus(job.id, 'processing', { step: 'extracting_media' });
      const ogImages = await extractOGImages(url);
      const favicon = await extractFavicon(url);
      
      // Optimize content for LLM
      await updateJobStatus(job.id, 'processing', { step: 'optimizing_content' });
      const optimizedContent = optimizeMarkdownForLLM(scrapeResult.content);
      
      // Validate content
      const validation = validateScrapedContent(optimizedContent, url);
      
      if (!validation.isValid) {
        console.warn(`Content validation issues for ${url}:`, validation.issues);
      }
      
      // Store results
      const result = {
        url,
        content: optimizedContent,
        ogImages,
        favicon,
        metadata: scrapeResult.metadata,
        validation,
        timestamp: new Date().toISOString()
      };
      
      await updateJobStatus(job.id, 'completed', { result });
      
      return {
        success: true,
        data: result
      };
      
    } catch (error) {
      await updateJobStatus(job.id, 'failed', { error: error.message });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}
```

---

*This integration guide provides the foundation for implementing robust web scraping capabilities using the scrape.do API. The implementation should handle errors gracefully, optimize costs through intelligent batching, and produce high-quality markdown content suitable for AI processing.*
