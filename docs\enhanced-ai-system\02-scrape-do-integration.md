# Scrape.do API Integration Guide

## Overview

This document provides comprehensive integration specifications for the scrape.do API, which serves as the primary web scraping service for the enhanced AI-powered content generation system. The integration focuses on extracting structured data in markdown format optimized for LLM consumption.

## API Configuration

### Authentication
```typescript
const SCRAPE_DO_CONFIG = {
  apiKey: process.env.SCRAPE_DO_API_KEY, // 8e7e405ff81145c4afe447610ddb9a7f785f494dddc
  baseUrl: 'https://api.scrape.do',
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 2000 // 2 seconds
};
```

### Basic Request Structure
```typescript
interface ScrapeRequest {
  token: string;
  url: string;
  output?: 'markdown' | 'raw';
  render?: boolean;
  super?: boolean; // Residential & Mobile proxy
  geoCode?: string; // Country targeting
  customHeaders?: boolean;
  screenShot?: boolean;
  fullScreenShot?: boolean;
  timeout?: number;
}
```

## Core Integration Functions

### 1. Basic Page Scraping
```typescript
async function scrapePage(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
  const config = {
    method: 'GET',
    url: `${SCRAPE_DO_CONFIG.baseUrl}/?token=${SCRAPE_DO_CONFIG.apiKey}&url=${encodeURIComponent(url)}`,
    headers: {
      'Content-Type': 'application/json'
    },
    params: {
      output: 'markdown', // Get markdown format for LLM processing
      render: true, // Enable JavaScript rendering
      timeout: 30000,
      ...options
    }
  };

  try {
    const response = await axios(config);
    return {
      success: true,
      content: response.data,
      metadata: extractMetadata(response.headers),
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return handleScrapeError(error, url);
  }
}
```

### 2. Open Graph Image Extraction
```typescript
async function extractOGImages(url: string): Promise<MediaAsset[]> {
  const scrapeResult = await scrapePage(url, {
    customHeaders: true,
    render: true
  });

  if (!scrapeResult.success) {
    throw new Error(`Failed to scrape ${url}: ${scrapeResult.error}`);
  }

  const ogImages = [];
  const content = scrapeResult.content;

  // Extract OG images from scraped content
  const ogImageRegex = /<meta\s+property=["']og:image["']\s+content=["']([^"']+)["']/gi;
  const twitterImageRegex = /<meta\s+name=["']twitter:image["']\s+content=["']([^"']+)["']/gi;
  const facebookImageRegex = /<meta\s+property=["']fb:image["']\s+content=["']([^"']+)["']/gi;

  let match;
  
  // Extract og:image
  while ((match = ogImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'og:image',
      url: resolveUrl(match[1], url),
      priority: 1
    });
  }

  // Extract twitter:image
  while ((match = twitterImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'twitter:image',
      url: resolveUrl(match[1], url),
      priority: 2
    });
  }

  // Extract facebook image
  while ((match = facebookImageRegex.exec(content)) !== null) {
    ogImages.push({
      type: 'facebook:image',
      url: resolveUrl(match[1], url),
      priority: 3
    });
  }

  return ogImages.sort((a, b) => a.priority - b.priority);
}
```

### 3. Screenshot Capture (Fallback)
```typescript
async function captureScreenshot(url: string, options: ScreenshotOptions = {}): Promise<ScreenshotResult> {
  const config = {
    method: 'GET',
    url: `${SCRAPE_DO_CONFIG.baseUrl}/?token=${SCRAPE_DO_CONFIG.apiKey}&url=${encodeURIComponent(url)}`,
    params: {
      screenShot: true,
      fullScreenShot: options.fullPage || false,
      width: options.width || 1200,
      height: options.height || 800,
      render: true,
      timeout: 30000
    }
  };

  try {
    const response = await axios(config);
    
    return {
      success: true,
      imageData: response.data, // Base64 encoded image
      format: 'png',
      dimensions: {
        width: options.width || 1200,
        height: options.height || 800
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}
```

### 4. Favicon Extraction
```typescript
async function extractFavicon(url: string): Promise<FaviconResult> {
  const scrapeResult = await scrapePage(url, {
    customHeaders: true,
    render: true
  });

  if (!scrapeResult.success) {
    throw new Error(`Failed to scrape ${url}: ${scrapeResult.error}`);
  }

  const content = scrapeResult.content;
  const faviconUrls = [];

  // Extract favicon URLs from various sources
  const faviconRegexes = [
    /<link\s+rel=["']icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']shortcut icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']apple-touch-icon["']\s+href=["']([^"']+)["']/gi,
    /<link\s+rel=["']apple-touch-icon-precomposed["']\s+href=["']([^"']+)["']/gi
  ];

  faviconRegexes.forEach(regex => {
    let match;
    while ((match = regex.exec(content)) !== null) {
      faviconUrls.push(resolveUrl(match[1], url));
    }
  });

  // Add default favicon.ico if no favicon found
  if (faviconUrls.length === 0) {
    const baseUrl = new URL(url).origin;
    faviconUrls.push(`${baseUrl}/favicon.ico`);
  }

  return {
    faviconUrls: [...new Set(faviconUrls)], // Remove duplicates
    primaryFavicon: faviconUrls[0] || null
  };
}
```

## Multi-Page Scraping

### 1. Discover Related Pages
```typescript
async function discoverRelatedPages(url: string): Promise<RelatedPages> {
  const scrapeResult = await scrapePage(url);
  
  if (!scrapeResult.success) {
    return { pricing: [], faq: [], features: [], about: [] };
  }

  const content = scrapeResult.content;
  const baseUrl = new URL(url).origin;
  
  // Extract links that might contain relevant information
  const linkRegex = /<a\s+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
  const links = [];
  let match;

  while ((match = linkRegex.exec(content)) !== null) {
    const href = resolveUrl(match[1], url);
    const text = match[2].toLowerCase().trim();
    
    links.push({ url: href, text, type: categorizeLink(text) });
  }

  return {
    pricing: links.filter(link => link.type === 'pricing').map(link => link.url),
    faq: links.filter(link => link.type === 'faq').map(link => link.url),
    features: links.filter(link => link.type === 'features').map(link => link.url),
    about: links.filter(link => link.type === 'about').map(link => link.url)
  };
}

function categorizeLink(text: string): string {
  const categories = {
    pricing: ['pricing', 'price', 'cost', 'plan', 'subscription', 'billing'],
    faq: ['faq', 'help', 'support', 'question', 'answer'],
    features: ['feature', 'capability', 'function', 'tool', 'service'],
    about: ['about', 'company', 'team', 'story', 'mission']
  };

  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      return category;
    }
  }

  return 'other';
}
```

### 2. Batch Scraping with Cost Optimization
```typescript
async function scrapeMultiplePages(
  urls: string[], 
  options: BatchScrapeOptions = {}
): Promise<BatchScrapeResult> {
  const results = [];
  const maxConcurrent = options.maxConcurrent || 3; // Limit concurrent requests
  const delay = options.delay || 2000; // Delay between requests
  
  // Process URLs in batches to manage costs
  for (let i = 0; i < urls.length; i += maxConcurrent) {
    const batch = urls.slice(i, i + maxConcurrent);
    
    const batchPromises = batch.map(async (url, index) => {
      // Add delay to avoid rate limiting
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      try {
        return await scrapePage(url, {
          output: 'markdown',
          render: true,
          timeout: 30000
        });
      } catch (error) {
        return {
          success: false,
          url,
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Log progress
    console.log(`Completed batch ${Math.floor(i / maxConcurrent) + 1}/${Math.ceil(urls.length / maxConcurrent)}`);
  }

  return {
    totalUrls: urls.length,
    successCount: results.filter(r => r.success).length,
    failureCount: results.filter(r => !r.success).length,
    results
  };
}
```

## Error Handling & Recovery

### 1. Comprehensive Error Handling
```typescript
function handleScrapeError(error: any, url: string): ScrapeResult {
  const errorResult: ScrapeResult = {
    success: false,
    url,
    error: error.message,
    timestamp: new Date().toISOString(),
    retryable: false
  };

  // Categorize errors for appropriate handling
  if (error.response) {
    const status = error.response.status;
    
    switch (status) {
      case 429: // Rate limited
        errorResult.retryable = true;
        errorResult.retryAfter = parseInt(error.response.headers['retry-after']) || 60;
        break;
      case 500:
      case 502:
      case 503:
      case 504: // Server errors
        errorResult.retryable = true;
        break;
      case 403: // Forbidden
        errorResult.error = 'Access denied - check API key or target site restrictions';
        break;
      case 404: // Not found
        errorResult.error = 'Target URL not found';
        break;
      default:
        errorResult.error = `HTTP ${status}: ${error.response.statusText}`;
    }
  } else if (error.code === 'ECONNABORTED') {
    errorResult.error = 'Request timeout';
    errorResult.retryable = true;
  } else if (error.code === 'ENOTFOUND') {
    errorResult.error = 'DNS resolution failed';
  }

  return errorResult;
}
```

### 2. Retry Mechanism
```typescript
async function scrapeWithRetry(
  url: string, 
  options: ScrapeOptions = {}, 
  maxRetries: number = 3
): Promise<ScrapeResult> {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await scrapePage(url, options);
      
      if (result.success) {
        return result;
      }
      
      if (!result.retryable) {
        return result; // Don't retry non-retryable errors
      }
      
      lastError = result;
      
      // Exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
    } catch (error) {
      lastError = handleScrapeError(error, url);
      
      if (!lastError.retryable) {
        break;
      }
    }
  }
  
  return lastError || {
    success: false,
    url,
    error: 'Max retries exceeded',
    timestamp: new Date().toISOString()
  };
}
```

## Data Processing & Optimization

### 1. Markdown Optimization for LLM
```typescript
function optimizeMarkdownForLLM(content: string): string {
  // Remove excessive whitespace
  content = content.replace(/\n{3,}/g, '\n\n');
  
  // Remove navigation and footer content
  content = content.replace(/^(Navigation|Menu|Footer)[\s\S]*?(?=\n#|\n\n|$)/gm, '');
  
  // Clean up HTML artifacts
  content = content.replace(/<[^>]*>/g, '');
  
  // Normalize headers
  content = content.replace(/^#{4,}/gm, '###');
  
  // Remove empty sections
  content = content.replace(/^#+\s*$\n/gm, '');
  
  // Limit content length for token management
  const maxLength = 50000; // Approximately 12-15K tokens
  if (content.length > maxLength) {
    content = content.substring(0, maxLength) + '\n\n[Content truncated for processing]';
  }
  
  return content.trim();
}
```

### 2. Content Validation
```typescript
function validateScrapedContent(content: string, url: string): ValidationResult {
  const issues = [];
  
  // Check minimum content length
  if (content.length < 100) {
    issues.push('Content too short - may indicate scraping failure');
  }
  
  // Check for error indicators
  const errorIndicators = ['404', 'not found', 'access denied', 'forbidden'];
  if (errorIndicators.some(indicator => content.toLowerCase().includes(indicator))) {
    issues.push('Content contains error indicators');
  }
  
  // Check for meaningful content
  const meaningfulContentRegex = /[a-zA-Z]{10,}/;
  if (!meaningfulContentRegex.test(content)) {
    issues.push('Content lacks meaningful text');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    contentLength: content.length,
    url
  };
}
```

## Integration with Job System

### 1. Job Processing Integration
```typescript
export class ScrapeJobHandler {
  async processScrapeJob(job: ScrapeJob): Promise<JobResult> {
    const { url, options = {} } = job.data;
    
    try {
      // Update job status
      await updateJobStatus(job.id, 'processing', { step: 'scraping' });
      
      // Perform scraping
      const scrapeResult = await scrapeWithRetry(url, options);
      
      if (!scrapeResult.success) {
        throw new Error(scrapeResult.error);
      }
      
      // Extract media assets
      await updateJobStatus(job.id, 'processing', { step: 'extracting_media' });
      const ogImages = await extractOGImages(url);
      const favicon = await extractFavicon(url);
      
      // Optimize content for LLM
      await updateJobStatus(job.id, 'processing', { step: 'optimizing_content' });
      const optimizedContent = optimizeMarkdownForLLM(scrapeResult.content);
      
      // Validate content
      const validation = validateScrapedContent(optimizedContent, url);
      
      if (!validation.isValid) {
        console.warn(`Content validation issues for ${url}:`, validation.issues);
      }
      
      // Store results
      const result = {
        url,
        content: optimizedContent,
        ogImages,
        favicon,
        metadata: scrapeResult.metadata,
        validation,
        timestamp: new Date().toISOString()
      };
      
      await updateJobStatus(job.id, 'completed', { result });
      
      return {
        success: true,
        data: result
      };
      
    } catch (error) {
      await updateJobStatus(job.id, 'failed', { error: error.message });
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}
```

---

*This integration guide provides the foundation for implementing robust web scraping capabilities using the scrape.do API. The implementation should handle errors gracefully, optimize costs through intelligent batching, and produce high-quality markdown content suitable for AI processing.*
